package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DEVOPS_ROLE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TOKEN;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.NAMESPACE_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.NAMESPACE_VALUE_50M;
import static com.intuit.identity.authz.sdk.constants.AuthZConstants.INTUIT_USERID;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.authz.WASAuthZClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AdminAPIConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionRbacConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.WasAuthorizeRequest;
import com.intuit.identity.authz.sdk.client.AuthZBatchRequestBuilder;
import com.intuit.identity.authz.sdk.client.AuthZErrorCode;
import com.intuit.identity.authz.sdk.client.AuthZRequestBuilder;
import com.intuit.identity.authz.sdk.exception.AuthZException;
import com.intuit.identity.authz.sdk.model.AuthZBatchResponse;
import com.intuit.identity.authz.sdk.model.AuthZDecision;
import com.intuit.identity.authz.sdk.model.AuthZRequest;
import com.intuit.identity.authz.sdk.model.AuthZResponse;
import com.intuit.identity.authz.sdk.model.Resource;
import com.intuit.v4.Authorization;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class AccessVerifier {

  private WASAuthZClient wasAuthZClient;
  private WASContextHandler contextHandler;
  private AdminAPIConfig adminAPIConfig;
  private DefinitionRbacConfig definitionRbacConfig;
  public static final String INTUIT_PRACTICE_FIRM_DATA = "intuit.practice.firmData";
  public static final String WAS_TASK_POLICY_ID = "irn.intuit.was.task";
  public static final String FIRM_DOCS_PUBLIC = "firm-docs-public";
  public static final String IDP_META_TAGS = "idp.metaTags";
  public static final String DEVPORTAL_POLICY = "irn:intuitcorp::platformexps:resource/7065039507767760447";


  /**
   * Checks if the given user has access to query/mutate the workflow data.
   *
   * @param wasAuthorizeRequest : WAS Authorization Request
   * @return true/false if given user(expert,customer) has access to read/write the workflow data.
   */
  @ServiceMetric(serviceName = ServiceName.AUTHZ)
  public boolean verifyAccess(WasAuthorizeRequest wasAuthorizeRequest) {

    try {
      Authorization authorization =
          new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
      AuthZRequest authZRequest =
          new AuthZRequestBuilder()
              .withSubject(WorkflowConstants.USER_ID, authorization.getAuthId())
              .withSubject(
                  WorkflowConstants.TICKET_KEYWORD,
                  authorization.get(INTUIT_TOKEN))
              .withSubject(
                  INTUIT_TID, contextHandler.get(WASContextEnums.INTUIT_TID))
              .withResource(
                  WorkflowConstants.OWNER_ID,
                  String.valueOf(wasAuthorizeRequest.getWorkflowOwnerId()))
              .withResource(WorkflowConstants.ID, INTUIT_PRACTICE_FIRM_DATA)
              .withResource(IDP_META_TAGS, FIRM_DOCS_PUBLIC)
              .withAction(WorkflowConstants.ID, wasAuthorizeRequest.getPermission().getName())
              .build();

      // Adding on behalf of header if present
      if (StringUtils.isNotEmpty(wasAuthorizeRequest.getOnBehalfOf())) {
        Resource resource = authZRequest.getResource();
        resource.setAttribute(WorkflowConstants.ON_BEHALF_OF, wasAuthorizeRequest.getOnBehalfOf());
        authZRequest.setResource(resource);
      }

      // Adding assignee if present
      if (StringUtils.isNotEmpty(wasAuthorizeRequest.getAssignee())) {
        Resource resource = authZRequest.getResource();
        resource.setAttribute(WorkflowConstants.ASSIGNEE, wasAuthorizeRequest.getAssignee());
        authZRequest.setResource(resource);
      }

      return handleAuthZResponse(wasAuthZClient.getAuthZClient().authorize(authZRequest));
    } catch (AuthZException ex) {
      // TODO: Needs handling in case of service timeouts
      throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, ex);
    }
  }

  private boolean handleAuthZResponse(AuthZResponse authZResponse) {
    boolean result = Boolean.FALSE;
    if (ObjectUtils.isNotEmpty(authZResponse)
        && AuthZDecision.PERMIT == authZResponse.getDecision()) {
      result = Boolean.TRUE;
      WorkflowLogger.logInfo("Operation allowed=%s", authZResponse.toString());
    } else {
      WorkflowLogger.logInfo("Operation denied=%s", authZResponse.toString());
      if (CollectionUtils.isNotEmpty(authZResponse.getObligations())) {
        WorkflowLogger.logInfo("All Obligations=%s", authZResponse.getObligations().toString());
      }
    }
    return result;
  }
  
  
  @ServiceMetric(serviceName = ServiceName.AUTHZ)
  public boolean verifyHumanTaskAccess(WasAuthorizeRequest wasAuthorizeRequest) {
    try {
      AuthZRequest authZRequest = commonAuthZRequest()
		  .withAction(WorkflowConstants.ID, wasAuthorizeRequest.getPermission().getHumanTaskPermission())
		  .withResource(
              ActivityConstants.ACTIVITY_DOMAIN,
              String.valueOf(wasAuthorizeRequest.getDomain()))
          /**
           * For QBLive, there will be taskOwner.
           * For TTLive, not sure if taskOwner will be present or not. Hence, ternary check on taskOwner.
           */
          .withResource(WorkflowConstants.OWNER_ID,
            		  null != wasAuthorizeRequest.getTaskOwnerId() ?
            				  String.valueOf(wasAuthorizeRequest.getTaskOwnerId()) :
            					  String.valueOf(wasAuthorizeRequest.getWorkflowOwnerId()))
          .withResource(ActivityConstants.TASK_TYPE, wasAuthorizeRequest.getTaskType())
          .build();
      return handleAuthZResponse(wasAuthZClient.getAuthZClient()
    		  .authorize(authZRequest));
    } catch (AuthZException ex) {
    	if(AuthZErrorCode.AUTHZ_ERROR_UNAUTHORIZED_ACCESS == ex.getAuthZErrorCode()) {
    		throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, ex);
    	}
    	throw new WorkflowGeneralException(WorkflowError.AUTHZ_REQUEST_FAILED, ex);
    }
  }

  /**
   * Verifies user access based on type of workflow and operation
   */
  @ServiceMetric(serviceName = ServiceName.AUTHZ)
  public boolean verifyUserAccess(String workflowType, String operation) {
    if(!definitionRbacConfig.isEnabled()){
      return true;
    }

    String realmId = contextHandler.get(WASContextEnums.OWNER_ID);
    String roles = definitionRbacConfig.getRolesAsString(workflowType,operation);

    Authorization authorization =
        new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
    try {
      AuthZRequest authZRequest = new AuthZRequestBuilder()
          .withAction(WorkflowConstants.ID, roles)
          .withResource(WorkflowConstants.ID, definitionRbacConfig.getGenericPolicyResource())
          .withSubject(INTUIT_USERID, contextHandler.get(WASContextEnums.INTUIT_USERID))
          .withSubject(INTUIT_REALMID, realmId)
          .withSubject(WorkflowConstants.INTUIT_TOKEN, authorization.get(INTUIT_TOKEN))
          .withSubject(WorkflowConstants.INTUIT_TID, contextHandler.get(WASContextEnums.INTUIT_TID))
          .build();
      return handleAuthZResponse(wasAuthZClient.getAuthZClient().authorize(authZRequest));
    } catch (AuthZException ex) {
      WorkflowLogger.logError("Error in Making Call to AuthZ=", ExceptionUtils.getStackTrace(ex));
      throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, ex);
    }
  }
  
  
  /**
   * 
   * Makes batch AuthZ call.
   * @param wasAuthorizeRequests
   * @return Map of taskId with boolean true/false for access of resource.
   */
  @ServiceMetric(serviceName = ServiceName.AUTHZ)
  public Map<String,Boolean> verifyBatchHumanTaskAccess(
		  List<WasAuthorizeRequest> wasAuthorizeRequests) {
    try {
      if(CollectionUtils.isEmpty(wasAuthorizeRequests)) {
        return Map.of();
      }
      AuthZRequest commonAuthZRequest = commonAuthZRequest().build();
      AuthZBatchRequestBuilder authZBatchRequestBuilder = 
    		  new AuthZBatchRequestBuilder().withCommon(commonAuthZRequest);
      
      for(WasAuthorizeRequest wasAuthorizeRequest:wasAuthorizeRequests) {
    	  AuthZRequest authZRequest =
    			  new AuthZRequestBuilder()
    			  .withAction(WorkflowConstants.ID, wasAuthorizeRequest.getPermission()
    					  .getHumanTaskPermission())
    			  .withResource(
    	                  ActivityConstants.ACTIVITY_DOMAIN,
    	                  String.valueOf(wasAuthorizeRequest.getDomain()))
    			  /**
    	           * For QBLive, there will be taskOwner.
    	           * For TTLive, not sure if taskOwner will be present or not. Hence, ternary check on taskOwner.
    	           */
    	          .withResource(WorkflowConstants.OWNER_ID,
    	            		  null != wasAuthorizeRequest.getTaskOwnerId() ?
    	            				  String.valueOf(wasAuthorizeRequest.getTaskOwnerId()) :
    	            					  String.valueOf(wasAuthorizeRequest.getWorkflowOwnerId()))
    	          .withResource(ActivityConstants.TASK_TYPE, wasAuthorizeRequest.getTaskType())
    	          .build();
    	  
		  authZBatchRequestBuilder.withAuthZRequest(authZRequest);
      }
          
      return handleAuthZBatchResponse(wasAuthorizeRequests,
    		  wasAuthZClient.getAuthZClient()
    		  .authorize(authZBatchRequestBuilder.build()));
    } catch (AuthZException ex) {
    	if(AuthZErrorCode.AUTHZ_ERROR_UNAUTHORIZED_ACCESS == ex.getAuthZErrorCode()) {
    		throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, ex);
    	}
    	throw new WorkflowGeneralException(WorkflowError.AUTHZ_REQUEST_FAILED, ex);

    }
  }
  
  /**
   * Prepares common subjects for Batch AuthZ request.
   * @return
   */
  private AuthZRequestBuilder commonAuthZRequest() {
	Authorization authorization =
          new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
      
	return new AuthZRequestBuilder()
			.withSubject(WorkflowConstants.USER_ID, authorization.getAuthId())
			.withSubject(INTUIT_REALMID, authorization.getRealm())
			.withSubject(WorkflowConstants.TICKET_KEYWORD, authorization.get(INTUIT_TOKEN))
			.withSubject(INTUIT_TID, contextHandler.get(WASContextEnums.INTUIT_TID))
			.withResource(WorkflowConstants.ID, WAS_TASK_POLICY_ID);
  }
  
  
  /**
   * Handles the list of response on Batch Authorize request.
   * @param wasAuthorizeRequests
   * @param authZBatchResponse
   * @return Map of taskId with boolean true/false for access of resource.
   */
  private Map<String,Boolean> handleAuthZBatchResponse(List<WasAuthorizeRequest> wasAuthorizeRequests,
		  AuthZBatchResponse authZBatchResponse) {
	
	Map<String,Boolean> authzBatchResponseMap = new HashMap<>();
	if(ObjectUtils.isEmpty(authZBatchResponse)) {
		return wasAuthorizeRequests.stream()
			.collect(Collectors.toMap(WasAuthorizeRequest::getTaskId, obj -> Boolean.FALSE));
	}
	IntStream.range(0,authZBatchResponse.getResponses().size()).forEach(index -> {
		AuthZResponse authZResp = authZBatchResponse.getResponses().get(index);
		WorkflowLogger.logInfo("AuthZ Operation taskId=%s decision=%s obligations=%s", 
				wasAuthorizeRequests.get(index).getTaskId(),
				authZResp.toString(), 
				CollectionUtils.isNotEmpty(authZResp.getObligations())? 
						authZResp.getObligations().toString() : "NA");
		boolean result = Boolean.FALSE;
		if (AuthZDecision.PERMIT == authZResp.getDecision()) {
			result = Boolean.TRUE;
		} 
		authzBatchResponseMap.put(wasAuthorizeRequests.get(index).getTaskId(), result);
	});
	return authzBatchResponseMap;
  }

    /**
     * Calls the dev-portal asset policy and checks if the given user has the required role
     *
     * @return true/false in case of permit or deny
     */
    @ServiceMetric(serviceName = ServiceName.AUTHZ)
    public boolean hasAdminAPIAccess() {
        if (!adminAPIConfig.isDevPortalAuthorizationEnabled()) {
            // skip validation in pre prod as policy can only be accessed in prod
            WorkflowLogger.logInfo("message=permission validation skipped");
            return true;
        }
        try {
            Authorization authorization =
                    new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
            AuthZRequest authZRequest = new AuthZRequestBuilder().withAction(WorkflowConstants.ID, DEVOPS_ROLE)
                    .withResource(WorkflowConstants.ID, DEVPORTAL_POLICY)
                    .withSubject(NAMESPACE_ID, NAMESPACE_VALUE_50M)
                    .withSubject(INTUIT_USERID, contextHandler.get(WASContextEnums.INTUIT_USERID))
                    .withSubject(INTUIT_REALMID,  authorization.get(INTUIT_REALMID))
                    .withSubject(INTUIT_TOKEN, authorization.get(INTUIT_TOKEN))
                    .withSubject(INTUIT_TID, contextHandler.get(WASContextEnums.INTUIT_TID)).build();
            boolean access = handleAuthZResponse(wasAuthZClient.getAuthZClient().authorize(authZRequest));
            WorkflowVerfiy.verify(!access,WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
            return access;
        } catch (AuthZException ex) {
            WorkflowLogger.logError("Error in Making Call to AuthZ=", ExceptionUtils.getStackTrace(ex));
            throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, ex);
        }
    }

}