<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
		<artifactId>workflow-automation-service-aggregator</artifactId>
		<version>1.1.20</version>
	</parent>

	<artifactId>was-core</artifactId>
	<packaging>jar</packaging>
	<properties>
		<slack.verion>1.39.2</slack.verion>
	</properties>
	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>was-common</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>was-dataaccess</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>was-connector</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>was-graphql-client</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.camunda.bpm.dmn</groupId>
			<artifactId>camunda-engine-dmn</artifactId>
		</dependency>
		<dependency>
			<groupId>org.camunda.bpm</groupId>
			<artifactId>camunda-engine-cdi</artifactId>
		</dependency>
		<dependency>
			<groupId>org.camunda.bpm.model</groupId>
			<artifactId>camunda-bpmn-model</artifactId>
		</dependency>
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
		</dependency>
		<dependency>
			<groupId>org.camunda.spin</groupId>
			<artifactId>camunda-spin-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.camunda.spin</groupId>
			<artifactId>camunda-spin-dataformat-json-jackson</artifactId>
		</dependency>
		<dependency>
			<groupId>org.camunda.bpm</groupId>
			<artifactId>camunda-engine-plugin-spin</artifactId>
		</dependency>
		<dependency>
			<groupId>org.camunda.bpm</groupId>
			<artifactId>camunda-engine-rest-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
			<artifactId>was-provideradapter</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
			<artifactId>was-eventpublisher</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>

		<!-- Test dependencies -->
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- cron util dependency -->
		<dependency>
			<groupId>com.cronutils</groupId>
			<artifactId>cron-utils</artifactId>
		</dependency>

		<dependency>
			<groupId>org.skyscreamer</groupId>
			<artifactId>jsonassert</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.vaadin.external.google</groupId>
					<artifactId>android-json</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- localisation dependency -->
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>was-localisation</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- library for testing using env variables -->
		<dependency>
			<groupId>uk.org.webcompere</groupId>
			<artifactId>system-stubs-junit4</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.camunda.bpm</groupId>
			<artifactId>camunda-external-task-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.vdurmont</groupId>
			<artifactId>semver4j</artifactId>
		</dependency>
		<dependency>
			<groupId>com.slack.api</groupId>
			<artifactId>slack-api-client</artifactId>
			<version>${slack.verion}</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${org.mapstruct.version}</version>
		</dependency>
		<dependency>
			<groupId>com.intuit.foundation.workflow</groupId>
			<artifactId>scheduling</artifactId>
			<version>${scheduling-iedm-schema.version}</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${org.mapstruct.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok-mapstruct-binding</artifactId>
							<version>${lombok-mapstruct-binding.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
