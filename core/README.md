Core Module:
--------------
 
1. This module acts as the business layer of WAS for various GraphQl and REST Operations.
2. This layer supports the CRUD operations on Template, Start and Signalling the process, Rule Evaluation Support for DMN etc.
3. Please find below the list of APIs[GraphQl and REST] supported here: 

    1. [Read One Template](docs/Read_One_Template.md) : 
 ```For Reading a single template based on the ID provided.```

    2. [Read All Template](docs/Read_All_Template.md) :
 ```Returns All the Enabled Multiple templates.```

    3. [Read One Definition](docs/Read_One_Definition.md) : 
 ```For Reading a single template definition based on the ID provided for a particular company [Realm].```

    4. [Read All Definition](docs/Read_All_Definition.md) :
 ```Returns All the Definitions matching the desired status and company criterias.```
 
    5. [Create Definition](docs/Create_Definition.md) : 
 ```For Creating a Workflow Definition.```

    6. [Update Definition](docs/Update_Definition.md) :
 ```For Updating a Workflow Definition.```
 
    7. [Enable Definition](docs/Enable_Definition.md) : 
 ```For Enabling a Workflow Definition.```

    8. [Disable Definition](docs/Disable_Definition.md) :
 ```For Disabling a Workflow Definition.```
 
    9. [Start Process Trigger Request API](docs/Process_Start.md) : 
 ```For Starting the process based on Trigger received.```

    10. [Signal Process Trigger Request API](docs/Process_Signal.md) :
 ```For Signalling the process based on Trigger received. [Approve/Reject the invoice```
 
    11. [Rule Evaluation API](docs/Evaluate_Rules.md) :
     ```For DMN Rule Evaluation, in case of Invoice Approval like workflows```
    
    12. [Delete All Workflows ](docs/DeleteAll_Workflows.md) : ```in case of downgrade we will like to delete all the workflows```
    
    12. [Trigger Workflow](docs/Trigger_API.md) : ```Trigger or signal a workflow```
     	
4. This Core Module is also responsible for handling various Orchestrations, Trigger Handling, BPMN Processing to UI and other critical business logic.

5. Environment: 

    1. QAL : 
 ```https://workflowautomation-qal.api.intuit.com.```

    2. E2e :
 ```https://workflowautomation-e2e.api.intuit.com.```

    3. Perf : 
 ```https://workflowautomation-perf.api.intuit.com.```

    4. Prod :
 ```https://workflowautomation.api.intuit.com.```
