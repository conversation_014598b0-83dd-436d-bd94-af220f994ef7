Enable Definition:
------------------
1. Updates the status in Definition table as enabled and internal_status as null
2. Execute App-connect activate workflow call.

Above steps happens in Sync and response is returned as Definition enabled Successfully.


#### Query :

```
mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {
  createWorkflows_Definition(input:$input_0){
  workflowsDefinitionEdge{
    node
    {
    id
    description
    name
    recordType
    template {
      id
    }
  }
 }
}
}
   
{
     "input_0": {
       "clientMutationId": "InvoiceAppo",
       "workflowsDefinition": {
         "description": "Enabling Definition",
         "status": "ENABLED",
         "name": "Enable Definition",
         "recordType" : "invoice",
         
         "id" : "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjkxMjM4YWJmYjY:373b02f7-7fd4-11ea-9823-9ad1d595bf2f",
         "template": {
           "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmI4MDJjYjFkZTM:96509c8d-f232-464f-a2aa-7eec1b1aaf5b"
         }
         }
     }
   }
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.workflowplugin",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
