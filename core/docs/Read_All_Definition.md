Read All Definition Query:
-------------------------
1. This query returns all the Workflow Definition existing in WAS which matches certain criteria.
2. It checks for the company id[Realm], if there exists a definition and retrieves it otherwise sends an Error response.
3. The definition returned has to match the following criteria:
- **Status = ENABLED and Internal_Status = null**
- **Status = DISABLED and Internal_Status = MARKED_FOR_DISABLED**

There are 2 Status values in the WAS
1. **ENABLED** [Enabled Definition]
2. **DISABLED** [Disabled Definition]

There are 3 internal status values in WAS
1. **MARKED_FOR_DISABLED** : implies the definition has been marked for Disable on the UI.
2. **MARKED_FOR_DELETED** : implies the definition has been deleted from the UI and consequently from the WAS DB.
3. **STALE_DEFINITION** : implies the outdated version of the definition in case user updates the current definition, previous existing definition will be marked Stale.


#### Query :

```
query {
        workflows{
          definitions{
            edges{
              node{
                name
                id
                description
                displayName
                recordType
                version
                status
                template{
                  id
                }
                meta{
                  created
                  createdBy {
                    id
                  }
                }
              }
            }
          }
        }
      }
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.workflowplugin",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
