Delete Definition:
-------------------
1. Delete Definition details from the Camunda and AppConnect. [Cascade deleting all the versions in Camunda]
2. Sets the internal status of Definition in WAS DB as MARKED_FOR_DELETE.

#### Query :

```
mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {
     createWorkflows_Definition(input:$input_0){
     workflowsDefinitionEdge{
       node
       {
       id
       description
     }
    }
   }
   }
   
   
   {
     "input_0": {
       "clientMutationId": "InvoiceAppo",
       "workflowsDefinition": {
    
         "deleted" : true,
         "id" : "djQuMTo5MTMwMzUwMzI3NTUyOTY2OjkxMjM4YWJmYjY:9caaabdc-896e-11ea-baeb-7202e7c9cf80"
         }
     }
   }

```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.workflowplugin",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
