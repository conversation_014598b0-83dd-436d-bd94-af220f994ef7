Workflow Automation Service Developer Guide
----------------------------

* Tools/Software
* Setup DB
* Build
* Deployment
* Configuration

  
##### Tools/Software


 - Make sure podman(https://podman.io/getting-started/) is running on your machine. To setup and start podman in your machine, execute setup-podman.sh
 - Use Intellij/Eclipse as IDE.[ Intellij License](https://wiki.intuit.com/display/SOE/JetBrains+License+Server+On-boarding+Process).
 - Install lombok Plugin.
 - Code format: [google-java-format](https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml) Or use the plugins for [Eclipse and Intellij](https://github.com/google/google-java-format#intellij).
 - Install SonarLint plugin in local to Catch bugs and vulnerabilities in your code.
 - Build the project and start the application as per below setup steps. As no profile is mentioned explicitly, application should start with default profile.
 - Login to a company in silver-relase/silver-develop and fetch the tkt,realmid,authid that will be required to make api calls in WAS else you can get 401 unauthorized exception in logs.
 - Install Java 11 before running WAS locally. Refer this wiki for steps to upgrade from java 8 to java 11 : https://wiki.intuit.com/pages/viewpage.action?spaceKey=Identity&title=Migration+Guide+to+Java+11
 
  
##### Setup DB


  - CD to the directory of docker-compose.yml.
  - Run `` ./setup-podman.sh ``
  - Run `` ./setup-db.sh ``
  - This should set up camunda db instance with required schema and user role

 
 Test DB connection with any SQL client
 
 ```
  1. database name: camunda
  2. host: localhost
  3. JDBC url: ****************************************
  4. username: sas
  5. password: Intuit01
```

##### Build and run Project

```
Got to project root run command-> mvn clean install
run command-> cd app
run command-> mvn spring-boot:run
navigate to https://localhost.intuit.com:8443/health/full to check if server is running
```
#### FAQ's
To resolve the liquibase error related to data capture role for file ```was_v9_0021.sql``` while running the application locally, you have two options:
 - Firstly, you can clean up the current database and set it up again by following the steps mentioned in the "Setting up the database" section, which involves running the ```./setup-db.sh``` file. 
 - Alternatively, you can manually execute the SQL commands specified in the ```./setup-db.sh``` file to create the necessary data capture roles. It's important to note that cleaning up the database is only necessary if you choose the first option.

##### Connecting to Camunda

By default, Local WAS will connect to be QAL deployment of Camunda. In order to deploy Camunda locally follow the instructions here << TBD >>.
	
If you are connecting the common Camunda deployment (i.e. QAL or any other common environment), your local WAS instance will not pick up the external tasks from any workflow BPMN even if you are creating and triggering the workflow from your local setup. In order for the local WAS setup to pick up the external tasks defined in workflow, you would need to use the unique topic and worker for your external tasks which is enabled only on your local setup. 
	
Add following to your ```application-default.yaml``` under ```workers``` and use the same topic Name in your external tasks in your workflow BPMN

```
workers:
	#should be unique name
	my-test123: 
		disable: false
		topicName: my-test123
		lockDuration: 60000
		maxTasks: 10
		asyncResponseTimeout: 10000
```


##### Deployment


* Repo to configure deployment details. [Detail](https://github.intuit.com/appintgwkflw-wkflautomate/workflow-automation-service-deployment)
* ArgoCD interface to Sync deployments & trigger new ones.
	[QAL](https://qbo.argocd.tools-k8s-prd.a.intuit.com/applications/appintgwkflw-wkflautomate-camundaservice-usw2-ppd-qal)
	[E2e](https://qbo.argocd.tools-k8s-prd.a.intuit.com/applications/appintgwkflw-wkflautomate-camundaservice-usw2-ppd-e2e)


##### Configuration

Repo to change configurations for different envs [Config Detail](https://github.intuit.com/appintgwkflw-wkflautomate/workflow-automation-service-config)

#### Steps To Execute in WAS to start a workflow.

```
1. You need to create Subscription via app connect subscription API and create connection or same can be done from Workflow HUB UI. Rest API Details Details Attached in Collections.json.
2. Call Save Template API.
3. Call  Read All Templates GraphQl Query.
4. Call Write Definition GraphQl Query via template id returned in Read All Template.
5. Call Trigger API to Start the Process for a given Invoice Id with entityChangeType as updated.You will receive email/notification based on the Email id Set in create Definition payload.
6. Call Trigger API With entityChangeType as Approved/Rejected.
```

#### Running Chaos Monkey on Local

```
java -jar  app/target/was-app.jar --spring.profiles.active=chaos-monkey,default
Use application-chaos-monkey.yaml for modifying any config changes for chaos monkey.
```

#### Refer this documentation to run bpmn with eventing in local WAS application.

Ability to publish and consume messages and complete external tasks locally.
[Complete external tasks locally](https://docs.google.com/document/d/11N7xjeD-qnh7BFes9I1BJGJXC8qYyFEklejAyqm0_FM/edit?usp=sharing)
