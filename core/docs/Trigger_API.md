Trigger API :
----------------------------
1. This API either starts the process or signals the  waiting process.
2. Here workflow is the template name in WAS that you want to trigger.It internally fetch the definition details and process details to either start the process or trigger the waiting process based on entityChangeType.
3. It uses entityChangeType to identify what action needs to be done.This entityChangeType is configured while saving the template in WAS.While saving the template we define the startable events : ["created", "updated"] based on this it will start the Process. 
4. If entityChangeType is other then the startable events then it will fetch the trigger details from WAS based on the different triggers in BPMN template and will signal the process that is listening on that event.
5. Based on the process variable configured while saving Template it will fetch those variables from the payload and will set as process variables.
6. entityType value and key in entity should match: In this case it should be invoice at both places. 

#### CURL Request : 

##### Sample request for starting the process.For triggering the process just need to provide valid value of entityChangeType.

```
curl --location --request POST 'https://localhost.intuit.com:8443/v1/trigger' \
--header 'Content-Type: application/json' \
--header 'intuit_tid: <TID>' \
--header 'Authorization: <Authorization>' \
--header 'Content-Type: text/plain' \
--data-raw '{ 
   "eventHeaders":{ 
   	  "workflow":"invoicereminder",
   	  "entityType":"invoice",
      "entityChangeType":"created"
   },
   "entity":{ 
      "Invoice":{ 
         "TotalAmt":800,
         "Id":"17",
         "invoiceDueDate":"2020-06-10T12:13:14.000+0530",
         "customerId":"customerid"
      }
   }
}'
```

#### Response : 

```
{
    "status": "SUCCESS",
    "response": {
        "processId": "88ab5b3e-a19d-11ea-a245-82faafc2ac2b",
        "status": "PROCESS_STARTED"
    }
}
```

#### Authentication Supported: It supports Private Auth and have enabled this as Offline API.

#### Private Auth Headers:

**1. Authorization :**
`Intuit_IAM_Authentication intuit_appid={{APP-ID}},intuit_app_secret={{APP-SECRET}},intuit_token={{TOKEN-ID}},intuit_userid={{USER-ID}},intuit_token_type=IAM-Ticket,intuit_realmid={{REALM-ID}}'

**2. intuit_tid :**
 `{{TID}}` For Tracking Related Purpose.
