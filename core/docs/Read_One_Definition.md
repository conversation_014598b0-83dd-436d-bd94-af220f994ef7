Read One Definition Query:
------------------------
1. This query is used to render the Definition Details on which Workflow UI is built. This displays all the definitions that user provided while creating a workflow definition.
2. It reads the template [BPMN File(XML)] stored in Camunda and creates a graphQl response that can be rendered on the Workflow Hub UI.
3. It checks for the company id[Realm], if there exists a definition and retrieves it otherwise sends an Error response.

#### Query :

```
query q{
   	node(id: "djQuMTo5MTMwMzUwMzI3NTUyOTY2OjkxMjM4YWJmYjY:9caaabdc-896e-11ea-baeb-7202e7c9cf80") {
       ... on Workflows_Definition {
         name
         id
         description
         displayName
         recordType
         meta{
               created
               createdBy {
                 id
               }
             }
         template{
           name
           id
           description
           workflowSteps {
           edges {
             node {
               id
               sequence
               required
               trigger{
                 name
                 id
                 required
                 parameters{
                     parameterName
                    parameterType
                     helpVariables
                     getOptionsForFieldValue
                     fieldValues
                     configurable
                     multiSelect
                     possibleFieldValues
                 }
               }
               actions{
                 actionKey
                 action{
                   name
                   id
                   required
                   selected
                   parameters{
                     parameterType
                     helpVariables
                     getOptionsForFieldValue
                     fieldValues
                     configurable
                     multiSelect
                     possibleFieldValues
                   }
                   nexts{
                     nextType
                     nextAction {
                       id
                     }
                     nextWorkflowStep {
                       id
                     }
                   }
                 }
               }
               workflowStepCondition{
                 id
                 required
                 description
                 deleted
                 ruleLines{
                   edges{
                     node{
                       mappedActionKeys
                       rules{
                         conditionalExpression
                         parameterName
                         conditionalExpression
                         selectedlogicalGroupingOperator
                       }
                     }
                   }
                 }
               }
             }
           }
         }
         }
       }
     }
   }
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.workflowplugin",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
