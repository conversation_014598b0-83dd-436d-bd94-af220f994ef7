Workflow Automation Service Creating a Workflow
----------------------------

### Steps for  creating simple workflow template.
  
Steps:

- [Install](https://camunda.com/download/modeler/?__hstc=12929896.1ef7706abf595f93146a152dbb578eb6.1565960338671.1592145894730.1592203122969.191&__hssc=12929896.4.1592203122969&__hsfp=1833772384) Camunda Modeler and create BPMN template in it.[Steps](https://docs.camunda.org/manual/7.9/modeler/camunda-modeler/bpmn/) for creating a BPMN template.
- Give template a Id and Name.id value should be : {recordtype}{workflow} like estimateapproval.
- In extension add property with name description and add a value to it.
- In the start event add the following keys in extension elements.
     - handlerDetails : `{"recordType": "estimate"}`
     - stepDetails: `{   "startEvent": [     "startEvent",     "helloWorld"   ] }`
     - startableEvents : `["created", "updated"]`
     - processVariablesDetails: `[{"variableName":"TotalAmt","variableType":"Double"},{"variableName":"Id","variableType":"String"},{"variableName":"intuit_userid","variableType": "String"},   {     "variableName": "intuit_realmid",     "variableType": "String"   } ]`
   
- Details
    - handlerDetails: uses the recordType value to identify which the the record type for which this workflow is ex:estimate,invoice,expense,timesheet.
    - stepDetails: is used to club actions in a given step.
    - startableEvents: will be used in trigger api based on this process will be started.
    - processVariablesDetails: will extract these values from the payload in trigger api and will set it as process variables.

Trigger payload:

```
{ 
   "eventHeaders":{ 
   	  "workflow":"approval",
   	  "entityType":"estimate",
      "entityChangeType":"updated"
   },
   "entity":{ 
      "Estimate":{ 
         "TotalAmt":800,
         "Id":"1"
      }
   }
}
```

- entityType should be same as recordId.
- entityChangeType should be the one of startableEvents to start the process
- entityType value should be the key in entity section that contains the direct payload.ex: here it should be Estimate.
- TotalAmt,Id if want to be added in processVariables should be there in processVariablesDetails.if any nested value is to be added then it should be like a_b_c format.

### Worker changes:
- Enable worker on local
- Use a different topic name in WAS worker config so that external taslk worker can pick it up. 
  
##### Steps to run notification.bpmn

 - Log into silver release company and make sure it have some invoices created.
 - Copy the ticket,authid and user id that will be used to make api call.
 - Call the template save API with notification.bpmn. Path [/was-app/src/main/resources/templates/examples/notification.bpmn]
 - Use Read All Template to get this new workflow template.
 - Using the template id create a definition out of it.
 - Call the trigger api tp start the workflow.
 - Email will be sent on the email id with the invoice details.
 

![Notification](img/notification.png "Simple Notification Flow")



Sample API Calls:

1. Save Template

```
 curl --location --request POST 'https://localhost.intuit.com:8443/v1/template/save' \
--header 'Authorization: Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="xxx",intuit_token="xxx",intuit_apikey="xxx",intuit_userid="xxx",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=xxx'\'',intuit_appid=myId' \
--header 'intuit_tid: test' \
--form 'template_metadata={"status":"enabled","creatorType":"system","allowMultipleDefs":false}' \
--form 'files=@/Users/<USER>/Desktop/notification.bpmn'

```

2. Read All Template.

```
curl --location --request POST 'https://localhost.intuit.com:8443/v4/graphql' \
--header 'Authorization: Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="xxx",intuit_token="xxx",intuit_apikey="xxx",intuit_userid="xxx",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=xxx'\''' \
--header 'intuit_tid: test1234' \
--header 'Content-Type: application/json' \
--data-raw '{"query":"{workflows{\n    templates{\n      edges{\n        node{\n          name\n          id\n          version\n          workflowSteps {\n            edges {\n              node {\n                id\n                name\n                required\n                trigger {\n                  id\n                  required\n                  parameters {\n                    parameterType\n                    required\n                  }\n                }\n                actions {\n                  actionKey\n                  action {\n                    id\n                    required\n                    parameters {\n                      parameterType\n                      required\n                    }\n                  }\n                }\n                workflowStepCondition {\n                  id\n                  required\n                  ruleLines{\n                    edges{\n                      node{\n                        rules{\n                          ruleId\n                          conditionalExpression\n                          parameterName\n                        }\n                      }\n                    }\n                  }\n                  conditionalInputParameters{\n                    inputParameter {\n                      parameterType\n                      required\n                    }\n                    supportedOperators {\n                      symbol\n                      description\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}","variables":{}}'
```

3. WAS Write Definition:

```
curl --location --request POST 'https://localhost.intuit.com:8443/v4/graphql' \
--header 'Authorization: Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="xxx",intuit_token="xxx",intuit_apikey="xxx",intuit_userid="xxx",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=xxx'\''' \
--header 'Accept: ' \
--header 'intuit_tid: t1t2t3t4t5t998' \
--header 'Content-Type: application/json' \
--data-raw '{"query":"mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {\n  createWorkflows_Definition(input:$input_0){\n  workflowsDefinitionEdge{\n    node\n    {\n    id\n    description\n    template {\n      id\n    }\n    workflowSteps {\n      edges {\n      \tnode {\n  \t\t\ttrigger {\n            id\n            name\n          }\n          workflowStepCondition {\n            id\n            ruleLines {\n              edges {\n                node {\n                  mappedActionKeys\n                  rules {\n                    conditionalExpression\n                    parameterName\n                    selectedlogicalGroupingOperator\n                  }\n                }\n              }\n            }\n          }\n        \tactions {\n        \t  actionKey\n        \t}\n        }\n      }\n    }\n  }\n }\n}\n}","variables":{"input_0":{"clientMutationId":"MyDummyTemplate","workflowsDefinition":{"description":"My Dummy Test deployment","status":"ENABLED","displayName":"My Dummy Test -05","name":"My Dummy Test- 05","recordType":"estimate","template":{"id":"djQuMTo5MTMwMzQ5MzgyODc4NTg2OmI4MDJjYjFkZTM:4acc3e25-dbe1-48e1-957b-071dfe0e66af"},"workflowSteps":[{"id":"s","actions":[{"action":{"id":"djQuMToxMjM0OmI4MDJjYjFkZTM:helloWorld","parameters":[{"parameterName":"Approver #1","fieldValues":["<EMAIL>"]},{"parameterName":"Subject","fieldValues":["Invoice [[DocNumber]] requires approval"]},{"parameterName":"Message","fieldValues":["Hello,\n\nInvoice [[DocNumber]] is pending approval. Please approve it at the earliest using this task manager link - https://qbo.intuit.com/login?pagereq=https://qbo.intuit.com/app/taskmanager  \n\nNote that invoices that are not approved for more than 30 days will remain unapproved.\n\nWarm regards,\n[[CompanyName]]"]}]}}]}]}}}}'
```

4. Trigger API:

```
curl --location --request POST 'https://localhost.intuit.com:8443/v1/trigger' \
--header 'Content-Type: application/json' \
--header 'intuit_tid: 439bb1b4-e8f5-48f7-a8ab-925d3066593a' \
--header 'Authorization: Intuit_IAM_Authentication intuit_appid=Intuit.appintgwkflw.wkflautomate.qbowasapiclient,intuit_app_secret=xxx,intuit_token=xxx,intuit_userid=xxx,intuit_token_type=IAM-Ticket,intuit_realmid=xxx' \
--header 'Content-Type: text/plain' \
--data-raw '{ 
   "eventHeaders":{ 
   	  "workflow":"approval",
   	  "entityType":"estimate",
      "entityChangeType":"updated"
   },
   "entity":{ 
      "Estimate":{ 
         "TotalAmt":800,
         "Id":"1"
      }
   }
}'
```
