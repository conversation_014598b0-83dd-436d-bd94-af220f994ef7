## Delete All Workflows Mutation Query

```
1. fetch all definition details for a given company.
2. update the internal status as MARKED_FOR_DELETE for all definition id of a company.
3. fetch auth details for a given company and populate auth details if not present
4. Initiate Delete all workflows command in async that does clean up in camunda,appconnect and WAS DB.

```

```
It happens in Async.

Response: 

{
    "data": {
        "WorkflowsBulkAction_deleteAllWorkflows": {
            "clientMutationId": "123",
            "workflowsBulkActionWorkflowBulkActionDetails": {
                "mode": "ASYNC",
                "inProgressEntities": [""]
            }
        }
    }
}

```

### Query: 

``` mutation downgrade($input_0: WorkflowsBulkAction_deleteAllWorkflowsInput!) {
 WorkflowsBulkAction_deleteAllWorkflows(input:$input_0){
  clientMutationId
  workflowsBulkActionWorkflowBulkActionDetails{
    mode
    inProgressEntities
    name
    action
  }
}
}

{
  "input_0": {
    "clientMutationId": "123",
    "workflowsBulkActionWorkflowBulkActionDetails": {
      "mode": "ASYNC",
      "name": "DELETE_ALL",
      "action": "DELETE"
    }
  }
}
```
