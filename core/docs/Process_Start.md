Process start:
--------------
1. This rest End point will start the Workflow [Camunda process].
2. For Start Process : "entityChangeType":"updated".

#### JSON Request :

```
{ 
   "eventHeaders":{ 
   	  "workflow":"approval",
   	  "entityType":"invoice",
      "entityChangeType":"updated"
   },
   "entity":{ 
      "Invoice":{ 
         "CurrencyRef":{ 
            "name":"United States Dollar",
            "value":"USD"
         },
         "EmailStatus":"NotSet",
         "AllowOnlineACHPayment":false,
         "AllowIPNPayment":false,
         "MetaData":{ 
            "CreateTime":"2020-01-30T21:37:01-08:00",
            "LastUpdatedTime":"2020-01-30T21:37:01-08:00"
         },
         "DocNumber":"1013",
         "PrintStatus":"NeedToPrint",
         "DueDate":"2020-03-01",
         "LinkedTxn":[ 
         ],
         "AllowOnlinePayment":false,
         "TxnDate":"2020-01-31",
         "DepartmentRef":{ 
            "name":"dep1",
            "value":"1"
         },
         "Line":[ 
            { 
               "LineNum":1,
               "DetailType":"SalesItemLineDetail",
               "Amount":2,
               "SalesItemLineDetail":{ 
                  "TaxCodeRef":{ 
                     "value":"NON"
                  },
                  "ItemAccountRef":{ 
                     "name":"Services",
                     "value":"4"
                  },
                  "ItemRef":{ 
                     "name":"Gardening",
                     "value":"3"
                  }
               },
               "Id":"1"
            },
            { 
               "SubTotalLineDetail":{ 
               },
               "DetailType":"SubTotalLineDetail",
               "Amount":2
            }
         ],
         "SyncToken":"0",
         "sparse":false,
         "TotalAmt":100,
         "domain":"QBO",
         "CustomField":[ 
         ],
         "SalesTermRef":{ 
            "value":"3"
         },
         "Id":"22",
         "Tag":[ 
         ],
         "AllowOnlineCreditCardPayment":false,
         "CustomerRef":{ 
            "name":"Customer1",
            "value":"100"
         },
         "Balance":2,
         "ApplyTaxAfterDiscount":false
      },
      "time":"2020-01-30T21:37:28.661-08:00"
   }
}
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.qbowasapiclient",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
