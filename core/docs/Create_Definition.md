Create Definition Mutation :
----------------------------
1. This mutation query aims to create new workflow definition.
2. Definition can be created with Enabled Status [**Save and Send Action on UI**] and Disabled Status [**Save Action**].
3. This query interacts with Camunda Service, AppConnect Service and WAS to create a definition.
4. Deploys the updated BPMN in Camunda, then tries to create workflow in AppConnect[Enabling the workflow is determined based on the status value] and then creating the entry in WAS's definitionDetails table. 
5. For now, Multiple Templates can be created with Status Disabled and only Single Template for a Company[Realm].
6. In case of attempting to create Multiple Enabled Workflow Definition, API returns an error '**Definition Already Exists**'.

#### Query :

```
mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {  
   createWorkflows_Definition(input: $input_0) {
   workflowsDefinitionEdge {
   node {
   id
   description
   name
   displayName
   template {
   id 
   }
   meta {
   created
   updated
   createdBy {
   id
   }
   }    
   recordType
   workflowSteps {
   edges {
   node {
       id
   trigger {
   id
   parameters {
   parameterName
   fieldValues
   required
   possibleFieldValues
   }
   }
   workflowStepCondition {
   id
   ruleLines {
   edges {
   node {
   mappedActionKeys
   rules {
   conditionalExpression
   parameterName
   selectedlogicalGroupingOperator
   
   }
   }
   }
   }
   }
   actions {
   actionKey
   action {
   name
   id
   required
   parameters {
      parameterName
   parameterType
   helpVariables
   getOptionsForFieldValue
   fieldValues
   configurable
   multiSelect
   }
   }
   }
   }
   }
   }
   
   }
   
   }
   
   }
   }


{
    "input_0": {
      "clientMutationId": "InvoiceAppo",
      "workflowsDefinition": {
        "status": "DISABLED",
        "name": "invoiceapproval",
        "displayName": "Test-write-Disabled",
        "template": {
          "id": "djQuMTo5MTMwMzUwMzI3NTUyOTY2OmI4MDJjYjFkZTM:cc41c653-418e-4aa2-9379-f8fc1b612ac4"
        },
        "recordType": "invoice",
        "workflowSteps": [
          {
            "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:newInvoiceCreated_invoiceapproval",
            "workflowStepCondition": {
              "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjEwMzVlOWJmYjc:decision_invoiceapproval",
              "ruleLines": [
                {
                  "rules": [
                    {
                      "parameterName": "Amount",
                      "conditionalExpression": "EQ 0"
                    }
                  ],
                  "mappedActionKeys": [
                    "approvalRequired"
                  ]
                }
              ]
            },
            "actions": [
              {
                "actionKey": "approvalRequired",
                "action": {
                  "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendForApproval_invoiceapproval",
                  "name": "Send for Approval",
                  "required": true,
                  "selected": true,
                  "nexts": [
                    {
                      "nextType": "WORFKLOWSTEP",
                      "nextAction": null,
                      "nextWorkflowStep": {
                        "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:1",
                        "__typename": "Workflows_WorkflowStep"
                      },
                      "__typename": "Workflows_Action_Next"
                    }
                  ],
                  "parameters": [
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "actionName",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "executeWorkflowAction"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "taskHandler",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "appconnect"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "handlerId",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "505836/521143"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "required",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "CC",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "My Company Email"
                      ],
                      "fieldValues": [],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "BCC",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "Customer Email"
                      ],
                      "fieldValues": [],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Message",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "DocNumber",
                        "InvoiceDate",
                        "DueDate",
                        "Balance",
                        "My Company Email"
                      ],
                      "fieldValues": [
                        "Invoice for [[CustomerName]] for $[[Amount]] requires approval.Click here https://silver-release.qbo.intuit.com/app/taskmanager to approve. \nRegards"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": "GET_ADMINS_ID",
                      "parameterName": "Approver #1",
                      "configurable": true,
                      "required": true,
                      "multiSelect": true,
                      "helpVariables": null,
                      "fieldValues": [
                        "<EMAIL>",
                        "<EMAIL>"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Send notification",
                      "configurable": false,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Send email",
                      "configurable": false,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": "GET_ADMINS_ID",
                      "parameterName": "Send To",
                      "configurable": true,
                      "required": true,
                      "multiSelect": true,
                      "helpVariables": null,
                      "fieldValues": [
                        "<EMAIL>",
                        "<EMAIL>"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Subject",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "DocNumber",
                        "InvoiceDate",
                        "DueDate",
                        "Balance",
                        "My Company Email"
                      ],
                      "fieldValues": [
                        "Invoice [[DocNumber]] requires approval"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    }
                  ],
                  "__typename": "Workflows_Action"
                },
                "__typename": "Workflows_WorkflowStep_ActionMapper"
              }
            ]
          },
          {
            "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:waitForTimerToElapse1_invoiceApproval",
            "trigger": {
              "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmExOTdjZjJhOTY:waitForTimerToElapse1_invoiceApproval",
              "required": true,
              "parameters": [
                {
                  "parameterName": "mappings",
                  "fieldValues": null,
                  "required": true,
                  "possibleFieldValues": [
                    "send reminder to approver:sendReminderEmail_invoiceApproval",
                    "Auto approve invoice:autoUpdateAsApproved_invoiceApproval"
                  ],
                  "__typename": "Workflows_Definitions_InputParameter"
                },
                {
                  "parameterName": "send reminder to approver",
                  "fieldValues": [
                    "send reminder to approver"
                  ],
                  "required": true,
                  "possibleFieldValues": [
                    "send reminder to approver",
                    "Auto approve invoice"
                  ],
                  "__typename": "Workflows_Definitions_InputParameter"
                },
                {
                  "parameterName": "Not Approved",
                  "fieldValues": [
                    "Not Approved"
                  ],
                  "required": true,
                  "possibleFieldValues": [
                    "Not Approved"
                  ],
                  "__typename": "Workflows_Definitions_InputParameter"
                },
                {
                  "parameterName": "waitTime",
                  "fieldValues": [
                    "5"
                  ],
                  "required": true,
                  "possibleFieldValues": null,
                  "__typename": "Workflows_Definitions_InputParameter"
                }
              ],
              "__typename": "Workflows_Trigger"
            },
            "actions": [
              {
                "actionKey": "sendApprovalReminder",
                "action": {
                  "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendReminderEmail_invoiceApproval",
                  "name": "Send reminder",
                  "required": false,
                  "selected": false,
                  "nexts": [
                    {
                      "nextType": "WORFKLOWSTEP",
                      "nextAction": null,
                      "nextWorkflowStep": {
                        "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:3",
                        "__typename": "Workflows_WorkflowStep"
                      },
                      "__typename": "Workflows_Action_Next"
                    }
                  ],
                  "parameters": [
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "actionName",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "executeWorkflowAction"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "taskHandler",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "appconnect"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "handlerId",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "505836/521143"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "required",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "CC",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "My Company Email"
                      ],
                      "fieldValues": [],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "BCC",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "Customer Email"
                      ],
                      "fieldValues": [],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Message",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "DocNumber",
                        "InvoiceDate",
                        "DueDate",
                        "Balance",
                        "My Company Email"
                      ],
                      "fieldValues": [
                        "Invoice for [[CustomerName]] for $[[Amount]] requires approval.Click here https://silver-release.qbo.intuit.com/app/taskmanager to approve. \nRegards"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": "GET_ADMINS_ID",
                      "parameterName": "Approver #1",
                      "configurable": true,
                      "required": true,
                      "multiSelect": true,
                      "helpVariables": null,
                      "fieldValues": [
                        "<EMAIL>","<EMAIL>"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Send notification",
                      "configurable": false,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Send email",
                      "configurable": false,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": "GET_ADMINS_ID",
                      "parameterName": "Send To",
                      "configurable": true,
                      "required": true,
                      "multiSelect": true,
                      "helpVariables": null,
                      "fieldValues": [
                        "<EMAIL>",
                        "<EMAIL>"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Subject",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "DocNumber",
                        "InvoiceDate",
                        "DueDate",
                        "Balance",
                        "My Company Email"
                      ],
                      "fieldValues": [
                        "Invoice [[DocNumber]] requires approval"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    }
                  ],
                  "__typename": "Workflows_Action"
                },
                "__typename": "Workflows_WorkflowStep_ActionMapper"
              },
              {
                "actionKey": "autoUpdate",
                "action": {
                  "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:autoUpdateAsApproved_invoiceApproval",
                  "name": "Auto approve invoice status",
                  "required": true,
                  "selected": false,
                  "nexts": [
                    {
                      "nextType": "ACTION",
                      "nextAction": {
                        "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendNotificationToCreator_invoiceapproval",
                        "__typename": "Workflows_Action"
                      },
                      "nextWorkflowStep": null,
                      "__typename": "Workflows_Action_Next"
                    }
                  ],
                  "parameters": [
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "actionName",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "executeWorkflowAction"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "taskHandler",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "appconnect"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "handlerId",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "intuit-workflows/520402"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "required",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    }
                  ],
                  "__typename": "Workflows_Action"
                },
                "__typename": "Workflows_WorkflowStep_ActionMapper"
              },
              {
                "actionKey": null,
                "action": {
                  "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmQ2NTUzY2E4NTI:sendNotificationToCreator_invoiceapproval",
                  "name": "Send notification to creator of invoice",
                  "required": false,
                  "selected": true,
                  "nexts": [
                    {
                      "nextType": "WORFKLOWSTEP",
                      "nextAction": null,
                      "nextWorkflowStep": {
                        "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OmMyNjEwYmRmYWI:3",
                        "__typename": "Workflows_WorkflowStep"
                      },
                      "__typename": "Workflows_Action_Next"
                    }
                  ],
                  "parameters": [
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "actionName",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "executeWorkflowAction"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "taskHandler",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "appconnect"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "handlerId",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "intuit-workflows/520508"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "required",
                      "configurable": null,
                      "required": null,
                      "multiSelect": null,
                      "helpVariables": null,
                      "fieldValues": [
                        "false"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "CC",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "My Company Email"
                      ],
                      "fieldValues": [],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "BCC",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "Customer Email"
                      ],
                      "fieldValues": [],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "STRING",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Message",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate",
                        "ApproverName",
                        "ApprovalStatusChangedDate"
                      ],
                      "fieldValues": [
                        "Hi,\n\nInvoice for [[CustomerName]] for $[[Amount]] got approved.\n\nWarm regards,\nQBO"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Send notification",
                      "configurable": false,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    },
                    {
                      "parameterType": "BOOLEAN",
                      "getOptionsForFieldValue": null,
                      "parameterName": "Send email",
                      "configurable": true,
                      "required": true,
                      "multiSelect": false,
                      "helpVariables": null,
                      "fieldValues": [
                        "true"
                      ],
                      "__typename": "Workflows_Definitions_InputParameter"
                    }
                  ],
                  "__typename": "Workflows_Action"
                },
                "__typename": "Workflows_WorkflowStep_ActionMapper"
              }
            ],
            "workflowStepCondition": {
              "id": "djQuMTo5MTMwMzQ3Nzk4MTIwMTA2OjEwMzVlOWJmYjc:evaluateUserDefinedAction_invoiceApproval",
              "conditionalInputParameters": [
                {
                  "inputParameter": {
                    "multiSelect": true,
                    "getOptionsForFieldValue": "GET_ADMINS_ID",
                    "parameterType": "STRING",
                    "parameterName": "To",
                    "__typename": "Workflows_Definitions_InputParameter"
                  },
                  "supportedOperators": [
                    {
                      "symbol": "CONTAINS",
                      "description": "Within",
                      "__typename": "Workflows_Definitions_Operator"
                    },
                    {
                      "symbol": "NOT_CONTAINS",
                      "description": "Not Within",
                      "__typename": "Workflows_Definitions_Operator"
                    }
                  ],
                  "__typename": "Workflows_Definitions_ConditionalParameter"
                },
                {
                  "inputParameter": {
                    "multiSelect": false,
                    "getOptionsForFieldValue": null,
                    "parameterType": "STRING",
                    "parameterName": "Message",
                    "__typename": "Workflows_Definitions_InputParameter"
                  },
                  "supportedOperators": [
                    {
                      "symbol": "CONTAINS",
                      "description": "Within",
                      "__typename": "Workflows_Definitions_Operator"
                    },
                    {
                      "symbol": "NOT_CONTAINS",
                      "description": "Not Within",
                      "__typename": "Workflows_Definitions_Operator"
                    }
                  ],
                  "__typename": "Workflows_Definitions_ConditionalParameter"
                },
                {
                  "inputParameter": {
                    "multiSelect": true,
                    "getOptionsForFieldValue": "GET_ADMINS_ID",
                    "parameterType": "STRING",
                    "parameterName": "To",
                    "__typename": "Workflows_Definitions_InputParameter"
                  },
                  "supportedOperators": [
                    {
                      "symbol": "CONTAINS",
                      "description": "Within",
                      "__typename": "Workflows_Definitions_Operator"
                    },
                    {
                      "symbol": "NOT_CONTAINS",
                      "description": "Not Within",
                      "__typename": "Workflows_Definitions_Operator"
                    }
                  ],
                  "__typename": "Workflows_Definitions_ConditionalParameter"
                },
                {
                  "inputParameter": {
                    "multiSelect": false,
                    "getOptionsForFieldValue": null,
                    "parameterType": "STRING",
                    "parameterName": "Subject",
                    "__typename": "Workflows_Definitions_InputParameter"
                  },
                  "supportedOperators": [
                    {
                      "symbol": "CONTAINS",
                      "description": "Within",
                      "__typename": "Workflows_Definitions_Operator"
                    },
                    {
                      "symbol": "NOT_CONTAINS",
                      "description": "Not Within",
                      "__typename": "Workflows_Definitions_Operator"
                    }
                  ],
                  "__typename": "Workflows_Definitions_ConditionalParameter"
                }
              ],
              "ruleLines": [
                {
                  "mappedActionKeys": [
                    "sendReminderEmail_invoiceApproval"
                  ],
                  "rules": [
                    {
                      "conditionalExpression": "${true}"
                    }
                  ]
                },
                {
                  "mappedActionKeys": [
                    "autoUpdateAsApproved_invoiceApproval"
                  ],
                  "rules": [
                    {
                      "conditionalExpression": "${false}"
                    }
                  ]
                }
              ],
              "__typename": "Workflows_WorkflowStepCondition"
            }
          }
        ]
      }
    }

}
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.workflowplugin",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
