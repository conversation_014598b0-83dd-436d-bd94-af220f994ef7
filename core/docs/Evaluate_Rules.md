Evaluate Rules:
--------------
1. This rest End point will evaluate Rules [invoke Camunda DMN] and provide the desired result.
2. In case of Invoice Approval Template, based on the input received for Amount [TotalAmt], Department(value) ["DepartmentRef":{"name":"dep1","value":"1"}], Customer(value) ["CustomerRef":{"name":"dep1","value":"1"}]. Rules are evaluated and output response is send.
3. This response determines, if the invoice has to be Auto-approved or it needs approval.

#### JSON Request :

```
{
   "eventHeaders":{ 
      "entityChangeType":"updated",
      "entityType":"invoice",
      "workflow":"approval"
   },
   "entity":{ 
      "Invoice":{ 
         "CurrencyRef":{ 
            "name":"United States Dollar",
            "value":"USD"
         },
         "EmailStatus":"NotSet",
         "AllowOnlineACHPayment":false,
         "AllowIPNPayment":false,
         "MetaData":{ 
            "CreateTime":"2020-01-30T21:37:01-08:00",
            "LastUpdatedTime":"2020-01-30T21:37:01-08:00"
         },
         "DocNumber":"610",
         "PrintStatus":"NeedToPrint",
         "DueDate":"2020-03-01",
         "LinkedTxn":[ 
         ],
         "AllowOnlinePayment":false,
         "TxnDate":"2020-01-31",
         "DepartmentRef":{ 
            "name":"dep1",
            "value":"1"
         },
         "Line":[ 
            { 
               "LineNum":1,
               "DetailType":"SalesItemLineDetail",
               "Amount":2,
               "SalesItemLineDetail":{ 
                  "TaxCodeRef":{ 
                     "value":"NON"
                  },
                  "ItemAccountRef":{ 
                     "name":"Services",
                     "value":"4"
                  },
                  "ItemRef":{ 
                     "name":"Gardening",
                     "value":"3"
                  }
               },
               "Id":"1"
            },
            { 
               "SubTotalLineDetail":{ 
               },
               "DetailType":"SubTotalLineDetail",
               "Amount":2
            }
         ],
         "SyncToken":"0",
         "sparse":false,
         "TotalAmt":1000,
         "domain":"QBO",
         "CustomField":[ 
         ],
         "SalesTermRef":{ 
            "value":"3"
         },
         "Id":"2",
         "Tag":[ 
         ],
         "AllowOnlineCreditCardPayment":false,
         "CustomerRef":{ 
            "name":"Customer1",
            "value":"10"
         },
         "Balance":1,
         "ApplyTaxAfterDiscount":false
      },
      "time":"2020-01-30T21:37:28.661-08:00"
   }
}
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.qbowasapiclient",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
