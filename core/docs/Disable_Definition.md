Disable Definition:
-------------------
1. Updates the status in Definition table as disabled and internal_status as MARKED_FOR_DISABLE
2. Updates the internal_status in Process table as MARKED_FOR_DISABLE

Above 2 steps happens in Sync and response is returned as Request taken successfully.

3. Signal the in flight process to end immediately via REST API on camunda.
4. Execute App-connect deactivate workflow call.

#### Query :

```
mutation Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {
     createWorkflows_Definition(input:$input_0){
     workflowsDefinitionEdge{
       node
       {
       id
       description
     }
    }
   }
   }
   
   
   {
     "input_0": {
       "clientMutationId": "InvoiceAppo",
       "workflowsDefinition": {
         "id": "djQuMTo5MTMwMzQ5MzgyODc4NTg2OjkxMjM4YWJmYjY:f70a3b14-7195-11ea-8a97-deb8e2b69331",
    			"status": "DISABLED"
       }
     }
   }
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.workflowplugin",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
