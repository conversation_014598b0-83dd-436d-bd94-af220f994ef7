Read All Template Query:
------------------------
1. This query returns all the Workflow Templates existing in WAS.
2. It queries WAS db and returns the latest version of the Enabled Template. Note the latest version is returned.


#### Query :

```
query workflows{
     workflows{
       templates{
         edges{
           node{
             name
             id
             version
             description
             category
             displayName
             allowMultipleDefinitions
            
           }
         }
       }
     }
   }
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.workflowplugin",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
