Read One Template Query:
------------------------
1. This query is used to render the Template Details on which Workflow UI is built.
2. It reads the template [BPMN File(XML)] and creates a graphQl response.


#### Query :

```
query q {
  node(id: "djQuMTo5MTMwMzUwMzI3NTUyOTY2OmI4MDJjYjFkZTM:cc41c653-418e-4aa2-9379-f8fc1b612ac4") {
    ... on Workflows_Template {
      name
      id
      description
      displayName
      version
      category
      workflowSteps {
        edges {
          node {
            id
            name
            required
            sequence
            trigger {
              id
              parameters {
                parameterType
                getOptionsForFieldValue
                parameterName
                configurable
                required
                multiSelect
                fieldValues
              }
            }
            workflowStepCondition {
              id
              conditionalInputParameters {
                inputParameter {
                  parameterType
                  getOptionsForFieldValue
                parameterName
                configurable
                required
                multiSelect
                fieldValues
                }
                supportedOperators {
                  symbol
                  description
                }
                
              }
              ruleLines{
                edges{
                  node{
            
                    rules{
                      parameterName
                      conditionalExpression
                    }
                  }
                }
              }
             
            }
            actions {
              actionKey
              action {
                  name
                  required
                  selected
                id
                nexts {
                  nextType
                  nextAction {
                    id
                  }
                  nextWorkflowStep {
                    id
                  }
                }
                parameters {
                  parameterType
                  getOptionsForFieldValue
                parameterName
                configurable
                required
                multiSelect
                fieldValues
                helpVariables
                }
              }
            }
          }
        }
      }
    }
  }
}
```


#### Required Headers:

**1. Authorization :**
`Intuit_APIKey intuit_token_type="IAM-Ticket",intuit_realmid="<REALM_ID>",intuit_token="<TOKEN_ID>",intuit_apikey="preprdakyresjTKSrxu8CngiQwYDUcafNMMQZzYY",intuit_appid="Intuit.appintgwkflw.wkflautomate.workflowplugin",intuit_userid="<USER_ID>",intuit_apkey_version="1.0" intuit_assetid: 86480sample2885,intuit_app_secret=<APP_SECRET>'

**2. intuit_tid (Optional)** : `<TID>` For Tracking Related Purpose.
