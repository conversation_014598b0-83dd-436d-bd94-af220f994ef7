package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.rbac;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionRbacConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class DefinitionRbacServiceTest {
    @Mock
    private AccessVerifier accessVerifier;
    @Mock
    private DefinitionRbacConfig definitionRbacConfig;
    @Mock
    private DefinitionServiceHelper definitionServiceHelper;
    @Mock
    private DefinitionService definitionService;
    @InjectMocks
    private DefinitionRbacService definitionRbacService;

    private static final String REALM_ID = "12345";
    private static final String DEFINITION_ID = "test-definition-id";
    private Definition definition;

    @Before
    public void setUp() {
        definition = new Definition();
        definition.setId(GlobalId.create(REALM_ID, DEFINITION_ID));
    }

    @Test
    public void testRbacDisabled_SkipsAccessCheck() {
        when(definitionRbacConfig.isEnabled()).thenReturn(false);
        definitionRbacService.verifyDefinitionAccess(definition, CrudOperation.CREATE);
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }

    @Test
    public void testRbacEnabled_AccessAllowed() {
        when(definitionRbacConfig.isEnabled()).thenReturn(true);
        // Mock for the actual call with null ownerId (since we're using the overloaded method)
        when(definitionServiceHelper.getWorkflowType(DEFINITION_ID, null)).thenReturn("approval");
        when(accessVerifier.verifyUserAccess("approval", "READ")).thenReturn(true);
        definition.setTemplate(null);
        definitionRbacService.verifyDefinitionAccess(definition, CrudOperation.READ);
        verify(accessVerifier, times(1)).verifyUserAccess("approval", "READ");
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testRbacEnabled_AccessDenied_ThrowsException() {
        when(definitionRbacConfig.isEnabled()).thenReturn(true);
        // Mock for the actual call with null ownerId (since we're using the overloaded method)
        when(definitionServiceHelper.getWorkflowType(DEFINITION_ID, null)).thenReturn("approval");
        when(accessVerifier.verifyUserAccess("approval", "DELETE")).thenReturn(false);
        definition.setTemplate(null);
        definitionRbacService.verifyDefinitionAccess(definition, CrudOperation.DELETE);
    }

    @Test
    public void testVerifyDefinitionAccess_WithTemplate() {
        Definition defWithTemplate = new Definition();
        Template template = new Template();
        template.setName("customApproval");  // Use a valid template name from CustomWorkflowType enum
        defWithTemplate.setTemplate(template);
        when(definitionRbacConfig.isEnabled()).thenReturn(true);
        // CustomWorkflowType.getActionKey("customApproval") returns "approval"
        when(accessVerifier.verifyUserAccess("approval", "CREATE")).thenReturn(true);
        // Don't set ID to simulate new definition
        definitionRbacService.verifyDefinitionAccess(defWithTemplate, CrudOperation.CREATE);
        verify(accessVerifier, times(1)).verifyUserAccess("approval", "CREATE");
    }

    @Test
    public void testMultipleOperations_VerifyAccessCalledCorrectly() {
        when(definitionRbacConfig.isEnabled()).thenReturn(true);
        // Mock for the actual call with null ownerId (since we're using the overloaded method)
        when(definitionServiceHelper.getWorkflowType(DEFINITION_ID, null)).thenReturn("approval");
        when(accessVerifier.verifyUserAccess("approval", "CREATE")).thenReturn(true);
        when(accessVerifier.verifyUserAccess("approval", "READ")).thenReturn(true);
        when(accessVerifier.verifyUserAccess("approval", "UPDATE")).thenReturn(true);
        when(accessVerifier.verifyUserAccess("approval", "DELETE")).thenReturn(true);
        definition.setTemplate(null);
        definitionRbacService.verifyDefinitionAccess(definition, CrudOperation.CREATE);
        definitionRbacService.verifyDefinitionAccess(definition, CrudOperation.READ);
        definitionRbacService.verifyDefinitionAccess(definition, CrudOperation.UPDATE);
        definitionRbacService.verifyDefinitionAccess(definition, CrudOperation.DELETE);
        verify(accessVerifier, times(1)).verifyUserAccess("approval", "CREATE");
        verify(accessVerifier, times(1)).verifyUserAccess("approval", "READ");
        verify(accessVerifier, times(1)).verifyUserAccess("approval", "UPDATE");
        verify(accessVerifier, times(1)).verifyUserAccess("approval", "DELETE");
    }
}
