<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="definitions_2064bd4c-4b24-4eb5-8cc3-bba34766045c" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="Camunda Modeler" exporterVersion="5.4.1">
  <process id="customApproval" name="Demo Multi Condition Definition Workflow with 7 Approvers new" processType="None" isClosed="false" isExecutable="true" camunda:historyTimeToLive="7">
    <startEvent id="startEvent" name="startEvent">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="stepDetails" value="{  &#34;customStartEvent_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585&#34;: [&#34;decisionElement&#34;] }" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;entityChangeType&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;entityType&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnAmount&#34;,&#34;variableType&#34;:&#34;double&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_userid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnBalanceAmount&#34;,&#34;variableType&#34;:&#34;double&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDueDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;DocNumber&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Id&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_realmid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Location&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true}]" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;,&#34;updated&#34;]" />
          <camunda:property name="elementType" value="implicit" />
        </camunda:properties>
      </extensionElements>
      <outgoing>sequenceFlow_to_decisionElement</outgoing>
    </startEvent>
    <sequenceFlow id="sequenceFlow_to_decisionElement" sourceRef="startEvent" targetRef="decisionElement" />
    <businessRuleTask id="decisionElement" name="Txn Rule Evaluation" implementation="##unspecified" camunda:type="external" camunda:topic="test-approval-yash">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>sequenceFlow_to_decisionElement</incoming>
      <outgoing>sequenceFlow_to_sendForApproval-zubair</outgoing>
      <outgoing>sequenceFlow_to_sendForApproval-vidhi</outgoing>
      <outgoing>sequenceFlow_to_sendForApproval-bidisha</outgoing>
      <outgoing>sequenceFlow_to_sendForApproval-bhavishya</outgoing>
      <outgoing>sequenceFlow_to_sendForApproval-shyam</outgoing>
      <outgoing>sequenceFlow_to_sendForApproval-manu</outgoing>
      <outgoing>sequenceFlow_to_sendForApproval-yash</outgoing>
      <outgoing>Flow_0w9kd1y</outgoing>
      <outgoing>Flow_1cciuj0</outgoing>
    </businessRuleTask>
    <sequenceFlow id="sequenceFlow_to_sendForApproval-zubair" sourceRef="decisionElement" targetRef="sendForApproval-zubair">
      <conditionExpression id="conditionExpression_afd89df2-7c2e-4e67-964a-6dcb296bac0e">${decisionResult=='sendForApproval-zubair'}</conditionExpression>
    </sequenceFlow>
    <callActivity id="sendForApproval-zubair" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:in businessKey="#{execution.processBusinessKey}" variables="all" />
        <camunda:out variables="all" />
      </extensionElements>
      <incoming>sequenceFlow_to_sendForApproval-zubair</incoming>
      <outgoing>Flow_1a2i0i7</outgoing>
    </callActivity>
    <sequenceFlow id="sequenceFlow_to_sendForApproval-vidhi" sourceRef="decisionElement" targetRef="sendForApproval-vidhi">
      <conditionExpression id="conditionExpression_334b38cf-5c45-4e28-af5f-73b190cee0d5">${decisionResult=='sendForApproval-vidhi'}</conditionExpression>
    </sequenceFlow>
    <callActivity id="sendForApproval-vidhi" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:in businessKey="#{execution.processBusinessKey}" variables="all" />
        <camunda:out variables="all" />
      </extensionElements>
      <incoming>sequenceFlow_to_sendForApproval-vidhi</incoming>
      <outgoing>sequenceFlow_50857339-81f6-42bf-819f-32a36cbc5e41</outgoing>
    </callActivity>
    <sequenceFlow id="sequenceFlow_to_sendForApproval-bidisha" sourceRef="decisionElement" targetRef="sendForApproval-bidisha">
      <conditionExpression id="conditionExpression_db6d0ae9-fba6-4590-b88b-22c3eb9b0f43">${decisionResult=='sendForApproval-bidisha'}</conditionExpression>
    </sequenceFlow>
    <callActivity id="sendForApproval-bidisha" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:in businessKey="#{execution.processBusinessKey}" variables="all" />
        <camunda:out variables="all" />
      </extensionElements>
      <incoming>sequenceFlow_to_sendForApproval-bidisha</incoming>
      <outgoing>sequenceFlow_fc6e1b75-52f2-4296-8892-c511b303074b</outgoing>
    </callActivity>
    <sequenceFlow id="sequenceFlow_to_sendForApproval-bhavishya" sourceRef="decisionElement" targetRef="sendForApproval-bhavishya">
      <conditionExpression id="conditionExpression_fac04077-dcc3-43ae-9ad2-e4caa2245637">${decisionResult=='sendForApproval-bhavishya'}</conditionExpression>
    </sequenceFlow>
    <callActivity id="sendForApproval-bhavishya" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:in businessKey="#{execution.processBusinessKey}" variables="all" />
        <camunda:out variables="all" />
      </extensionElements>
      <incoming>sequenceFlow_to_sendForApproval-bhavishya</incoming>
      <outgoing>sequenceFlow_0f1b48d7-63c8-4727-b16a-ca4fe2d7c100</outgoing>
    </callActivity>
    <sequenceFlow id="sequenceFlow_to_sendForApproval-shyam" sourceRef="decisionElement" targetRef="sendForApproval-shyam">
      <conditionExpression id="conditionExpression_9b815459-d90e-427b-b1cb-23e0b96d55b9">${decisionResult=='sendForApproval-shyam'}</conditionExpression>
    </sequenceFlow>
    <callActivity id="sendForApproval-shyam" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:in businessKey="#{execution.processBusinessKey}" variables="all" />
        <camunda:out variables="all" />
      </extensionElements>
      <incoming>sequenceFlow_to_sendForApproval-shyam</incoming>
      <outgoing>sequenceFlow_d623b9d8-ce41-4ecc-863c-cd4179a6ebcf</outgoing>
    </callActivity>
    <sequenceFlow id="sequenceFlow_to_sendForApproval-manu" sourceRef="decisionElement" targetRef="sendForApproval-manu">
      <conditionExpression id="conditionExpression_45f83672-3cda-4ff6-9c95-8c1e64c4e369">${decisionResult=='sendForApproval-manu'}</conditionExpression>
    </sequenceFlow>
    <callActivity id="sendForApproval-manu" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:in businessKey="#{execution.processBusinessKey}" variables="all" />
        <camunda:out variables="all" />
      </extensionElements>
      <incoming>sequenceFlow_to_sendForApproval-manu</incoming>
      <outgoing>sequenceFlow_1fea3426-fef8-4c26-8b73-d187a2bd33b2</outgoing>
    </callActivity>
    <sequenceFlow id="sequenceFlow_to_sendForApproval-yash" sourceRef="decisionElement" targetRef="sendForApproval-yash">
      <conditionExpression id="conditionExpression_65b9acf7-0a76-4efc-92df-ccf50ba9e5f9">${decisionResult=='sendForApproval-yash'}</conditionExpression>
    </sequenceFlow>
    <callActivity id="sendForApproval-yash" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:in businessKey="#{execution.processBusinessKey}" variables="all" />
        <camunda:out variables="all" />
      </extensionElements>
      <incoming>sequenceFlow_to_sendForApproval-yash</incoming>
      <outgoing>sequenceFlow_ae0320cd-8b5d-4720-945b-d9075aca2976</outgoing>
    </callActivity>
    <endEvent id="endEvent" name="endEvent">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="implicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>sequenceFlow_50857339-81f6-42bf-819f-32a36cbc5e41</incoming>
      <incoming>sequenceFlow_1fea3426-fef8-4c26-8b73-d187a2bd33b2</incoming>
      <incoming>sequenceFlow_ae0320cd-8b5d-4720-945b-d9075aca2976</incoming>
      <incoming>sequenceFlow_d623b9d8-ce41-4ecc-863c-cd4179a6ebcf</incoming>
      <incoming>sequenceFlow_0f1b48d7-63c8-4727-b16a-ca4fe2d7c100</incoming>
      <incoming>sequenceFlow_fc6e1b75-52f2-4296-8892-c511b303074b</incoming>
      <incoming>Flow_1cciuj0</incoming>
      <incoming>Flow_1a2i0i7</incoming>
      <messageEventDefinition id="messageEventDefinition_bf995181-bccd-495a-aa03-ecc6d4ae9804" messageRef="message_15374f00-e893-4073-94dc-bd0d88fc0548" camunda:type="external" camunda:topic="test-approval-yash" />
    </endEvent>
    <sequenceFlow id="sequenceFlow_50857339-81f6-42bf-819f-32a36cbc5e41" sourceRef="sendForApproval-vidhi" targetRef="endEvent" />
    <sequenceFlow id="sequenceFlow_1fea3426-fef8-4c26-8b73-d187a2bd33b2" sourceRef="sendForApproval-manu" targetRef="endEvent" />
    <sequenceFlow id="sequenceFlow_ae0320cd-8b5d-4720-945b-d9075aca2976" sourceRef="sendForApproval-yash" targetRef="endEvent" />
    <sequenceFlow id="sequenceFlow_d623b9d8-ce41-4ecc-863c-cd4179a6ebcf" sourceRef="sendForApproval-shyam" targetRef="endEvent" />
    <sequenceFlow id="sequenceFlow_0f1b48d7-63c8-4727-b16a-ca4fe2d7c100" sourceRef="sendForApproval-bhavishya" targetRef="endEvent" />
    <sequenceFlow id="sequenceFlow_fc6e1b75-52f2-4296-8892-c511b303074b" sourceRef="sendForApproval-bidisha" targetRef="endEvent" />
    <sequenceFlow id="Flow_0w9kd1y" name="Rules not satisfied" sourceRef="decisionElement" targetRef="autoApprove_Txn_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585">
      <conditionExpression id="conditionExpression_d908f6cb-48be-4f32-88f6-e47dd0f5cfde">${decisionResult == 'approval-0'}</conditionExpression>
    </sequenceFlow>
    <serviceTask id="autoApprove_Txn_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585" name="Approve Txn" implementation="##WebService" camunda:type="external" camunda:topic="test-approval-yash">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="implicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "appconnect",
    "handlerId": "intuit-workflows/update-txn-status",
    "actionName": "executeWorkflowAction"
}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
    "Id": {
        "fieldValue": [],
        "handlerFieldName": "txnId",
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "txnStatus": {
        "fieldValue": [
            "AUTO_APPROVED"
        ],
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "intuit_realmid": {
        "fieldValue": [],
        "handlerFieldName": "realmId",
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    }
}</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </extensionElements>
      <incoming>Flow_0w9kd1y</incoming>
      <outgoing>Flow_16xgdfu</outgoing>
    </serviceTask>
    <sequenceFlow id="Flow_16xgdfu" sourceRef="autoApprove_Txn_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585" targetRef="Event_0mlq58t_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585" />
    <endEvent id="Event_0mlq58t_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585" name="Event_0mlq58t_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="implicit" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>Flow_16xgdfu</incoming>
    </endEvent>
    <sequenceFlow id="Flow_1cciuj0" name="Auto Approved" sourceRef="decisionElement" targetRef="endEvent">
      <conditionExpression id="conditionExpression_44798018-b3b0-4450-b9da-a077d30f9d27">${decisionResult == 'false'}</conditionExpression>
    </sequenceFlow>
    <subProcess id="Activity_108jjaa" name="" triggeredByEvent="true">
      <startEvent id="Event_0vepyso" name="Close task">
        <outgoing>sequenceFlow_c7c2ed87-6467-4150-91af-06c3a3bd372b</outgoing>
        <escalationEventDefinition id="escalationEventDefinition_633d05a7-5084-45b2-ac67-dec24436482c" escalationRef="escalation_b0428314-afc2-4025-ade6-118db88d3922" />
      </startEvent>
      <serviceTask id="sendRejectNotification_txnApproval" name="Send notification to creator of transaction" implementation="##WebService" camunda:type="external" camunda:topic="test-approval-yash">
        <extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;, &#34;end&#34;]" />
          </camunda:properties>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "appconnect",
    "handlerId": "/intuit-workflows/api/was-send-notification-to-txn-creator.json",
    "actionName": "executeDuzzitRestAction"
}</camunda:inputParameter>
            <camunda:inputParameter name="parameterDetails">{
    "intuit_userid": {
        "handlerFieldName": "To",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Subject": {
        "fieldValue": [
            "~custom.approval.sendRejectNotification.txnApproval.Subject"
        ],
        "configurable": true,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "DocNumber": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "DocNumber"
    },
    "entityType": {
        "handlerFieldName": "RecordType",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Message": {
        "fieldValue": [
            "~custom.approval.sendRejectNotification.txnApproval.Message"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "Id": {
        "handlerFieldName": "TxnId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "intuit_realmid": {
        "handlerFieldName": "realmId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "isEmail": {
        "fieldValue": [
            "true"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "isMobile": {
        "fieldValue": [
            "true"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "CC": {
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "BCC": {
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "helpVariables": [
            "Customer Email"
        ],
        "multiSelect": false,
        "fieldType": "string"
    },
    "NotificationAction": {
        "fieldValue": [
            "qb001://open/{{RecordType}}/?id=[[{{RecordType}}LocalId]]&amp;companyid=[[realmId]]"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "handlerFieldName": "Mobile Notification Action"
    },
    "CompanyName": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "CompanyName"
    }
}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
            <camunda:outputParameter name="outputResponse" />
          </camunda:inputOutput>
        </extensionElements>
        <incoming>sequenceFlow_c7c2ed87-6467-4150-91af-06c3a3bd372b</incoming>
        <outgoing>sequenceFlow_2292c372-039a-46ca-a59d-1b0e71bb4a2b</outgoing>
      </serviceTask>
      <sequenceFlow id="sequenceFlow_c7c2ed87-6467-4150-91af-06c3a3bd372b" sourceRef="Event_0vepyso" targetRef="sendRejectNotification_txnApproval" />
      <endEvent id="Event_0ey8tt4" name="End process">
        <extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </extensionElements>
        <incoming>sequenceFlow_2292c372-039a-46ca-a59d-1b0e71bb4a2b</incoming>
        <messageEventDefinition id="messageEventDefinition_ba538a0e-7017-43a8-84ed-32690e87bff3" messageRef="message_15374f00-e893-4073-94dc-bd0d88fc0548" />
      </endEvent>
      <sequenceFlow id="sequenceFlow_2292c372-039a-46ca-a59d-1b0e71bb4a2b" sourceRef="sendRejectNotification_txnApproval" targetRef="Event_0ey8tt4" />
    </subProcess>
    <sequenceFlow id="Flow_1a2i0i7" sourceRef="sendForApproval-zubair" targetRef="endEvent" />
  </process>
  <message id="message_15374f00-e893-4073-94dc-bd0d88fc0548" name="process_ended_message" />
  <escalation id="escalation_b0428314-afc2-4025-ade6-118db88d3922" escalationCode="close_parent" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_11828e45-4192-4b03-8b47-e094d54ecd01">
    <bpmndi:BPMNPlane id="BPMNPlane_479c73a0-79b3-49d0-9ad6-537dfe757243" bpmnElement="customApproval">
      <bpmndi:BPMNShape id="BPMNShape_9e5bb161-f0e2-4a5a-9faa-e2d343f30d92" bpmnElement="startEvent">
        <dc:Bounds x="160" y="230" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="153" y="266" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_66060474-4710-41df-935c-3aede0bf9c0d" bpmnElement="decisionElement">
        <dc:Bounds x="246" y="208" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_2dda5f4f-53a6-44b3-a042-0b8fcea998c2" bpmnElement="sendForApproval-zubair">
        <dc:Bounds x="396" y="208" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_6d159d24-7372-426f-9bb4-942e86b45776" bpmnElement="sendForApproval-vidhi">
        <dc:Bounds x="396" y="338" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_46cb920f-05db-449d-b6ad-213fd3de4979" bpmnElement="sendForApproval-bidisha">
        <dc:Bounds x="396" y="468" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_734704e9-23b6-46f0-b854-b03ceda8357a" bpmnElement="sendForApproval-bhavishya">
        <dc:Bounds x="396" y="598" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_b8dc4aac-cb42-4935-831a-25ec6e0268b6" bpmnElement="sendForApproval-shyam">
        <dc:Bounds x="396" y="728" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_c2169154-9f7d-4ca9-92c6-b13b462611d4" bpmnElement="sendForApproval-manu">
        <dc:Bounds x="396" y="858" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_35c339ad-2cdf-4360-8224-bccfb88b82e6" bpmnElement="sendForApproval-yash">
        <dc:Bounds x="396" y="988" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0168bcbb-45ca-4f6c-8ed8-0f44e821ce22" bpmnElement="endEvent">
        <dc:Bounds x="932" y="230" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="977.5" y="241" width="47" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_f384ce2e-9c02-42f9-8f61-b792a3603e00" bpmnElement="autoApprove_Txn_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585">
        <dc:Bounds x="396" y="1118" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_291a627f-b42b-4496-83f1-8ec6b95338f7" bpmnElement="Event_0mlq58t_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585">
        <dc:Bounds x="546" y="1140" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="521" y="1176" width="87" height="66" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_ee8c9a31-cf78-4a13-9842-19f7447c906e" bpmnElement="Activity_108jjaa" isExpanded="true">
        <dc:Bounds x="160" y="1248" width="372" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_ad5d8f4e-f1d1-4b7f-8e84-b2b1dbfd2dfe" bpmnElement="Event_0vepyso">
        <dc:Bounds x="210" y="1330" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="202" y="1366" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_9ff20dc2-0ea0-432b-b691-b4057e9ae39b" bpmnElement="sendRejectNotification_txnApproval">
        <dc:Bounds x="296" y="1308" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_ca02308f-0ca2-4d61-8027-b75084813ccf" bpmnElement="Event_0ey8tt4">
        <dc:Bounds x="446" y="1330" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="433" y="1366" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_4a270e93-2b5b-4e3b-a1a0-2aec72da5625" bpmnElement="sequenceFlow_c7c2ed87-6467-4150-91af-06c3a3bd372b">
        <di:waypoint x="246" y="1348" />
        <di:waypoint x="296" y="1348" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_78d0c132-e37d-447d-8cb0-bee572378060" bpmnElement="sequenceFlow_2292c372-039a-46ca-a59d-1b0e71bb4a2b">
        <di:waypoint x="396" y="1348" />
        <di:waypoint x="446" y="1348" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_683414d1-7829-432e-8c16-51d242a2eff3" bpmnElement="sequenceFlow_to_decisionElement">
        <di:waypoint x="196" y="248" />
        <di:waypoint x="246" y="248" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_a6241b13-d163-4cd0-96e5-53b9081861d3" bpmnElement="sequenceFlow_to_sendForApproval-zubair">
        <di:waypoint x="346" y="248" />
        <di:waypoint x="396" y="248" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_b8c868fa-bab4-4554-85f9-5779420eed76" bpmnElement="sequenceFlow_to_sendForApproval-vidhi">
        <di:waypoint x="296" y="288" />
        <di:waypoint x="296" y="378" />
        <di:waypoint x="396" y="378" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_f6194370-9a63-45cf-9809-b5edd87c0f3a" bpmnElement="sequenceFlow_to_sendForApproval-bidisha">
        <di:waypoint x="296" y="288" />
        <di:waypoint x="296" y="508" />
        <di:waypoint x="396" y="508" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_3727642f-7e97-40f0-8603-7a8434fdc8c3" bpmnElement="sequenceFlow_to_sendForApproval-bhavishya">
        <di:waypoint x="296" y="288" />
        <di:waypoint x="296" y="638" />
        <di:waypoint x="396" y="638" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_f3bf2b16-3629-4e26-aecc-d8de8ae4c922" bpmnElement="sequenceFlow_to_sendForApproval-shyam">
        <di:waypoint x="296" y="288" />
        <di:waypoint x="296" y="768" />
        <di:waypoint x="396" y="768" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_5d9223be-ddf2-413b-95f3-481667840a7d" bpmnElement="sequenceFlow_to_sendForApproval-manu">
        <di:waypoint x="296" y="288" />
        <di:waypoint x="296" y="898" />
        <di:waypoint x="396" y="898" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_fa6395de-167d-4780-9164-339c5b7b77e3" bpmnElement="sequenceFlow_to_sendForApproval-yash">
        <di:waypoint x="296" y="288" />
        <di:waypoint x="296" y="1028" />
        <di:waypoint x="396" y="1028" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_79937a19-33bf-42f6-8516-068502aad72b" bpmnElement="sequenceFlow_50857339-81f6-42bf-819f-32a36cbc5e41">
        <di:waypoint x="496" y="378" />
        <di:waypoint x="950" y="380" />
        <di:waypoint x="950" y="266" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_ad566fe4-28df-4e66-9059-3e43b2041c73" bpmnElement="sequenceFlow_1fea3426-fef8-4c26-8b73-d187a2bd33b2">
        <di:waypoint x="496" y="898" />
        <di:waypoint x="950" y="900" />
        <di:waypoint x="950" y="266" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_f5af8b6b-523f-4331-8bcc-aa2b773b548c" bpmnElement="sequenceFlow_ae0320cd-8b5d-4720-945b-d9075aca2976">
        <di:waypoint x="496" y="1028" />
        <di:waypoint x="950" y="1028" />
        <di:waypoint x="950" y="266" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_a820c54c-efd8-420d-a102-470b135fa93a" bpmnElement="sequenceFlow_d623b9d8-ce41-4ecc-863c-cd4179a6ebcf">
        <di:waypoint x="496" y="768" />
        <di:waypoint x="950" y="768" />
        <di:waypoint x="950" y="266" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_3b9e5a77-714d-4a09-a9da-fb87245bd6a2" bpmnElement="sequenceFlow_0f1b48d7-63c8-4727-b16a-ca4fe2d7c100">
        <di:waypoint x="496" y="638" />
        <di:waypoint x="950" y="638" />
        <di:waypoint x="950" y="266" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_b1321550-1e90-4e9c-81c3-f715177ea977" bpmnElement="sequenceFlow_fc6e1b75-52f2-4296-8892-c511b303074b">
        <di:waypoint x="496" y="508" />
        <di:waypoint x="950" y="508" />
        <di:waypoint x="950" y="266" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_af64cde0-fc7e-4e3d-8caf-045d14e8d400" bpmnElement="Flow_0w9kd1y">
        <di:waypoint x="296" y="288" />
        <di:waypoint x="296" y="1158" />
        <di:waypoint x="396" y="1158" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="266" y="713" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_2b8e1fe1-e150-481c-8aa2-bc7f8960f50e" bpmnElement="Flow_16xgdfu">
        <di:waypoint x="496" y="1158" />
        <di:waypoint x="546" y="1158" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_7b3dd821-5df7-433f-a403-8d2283c793e1" bpmnElement="Flow_1cciuj0">
        <di:waypoint x="296" y="208" />
        <di:waypoint x="296" y="80" />
        <di:waypoint x="950" y="80" />
        <di:waypoint x="950" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="635" y="89" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a2i0i7_di" bpmnElement="Flow_1a2i0i7">
        <di:waypoint x="496" y="248" />
        <di:waypoint x="932" y="248" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>