{"bpmn_placeholder_values": {"process_variables": {"VendorName": {"type": "string"}, "entityChangeType": {"type": "String"}, "TxnDate": {"type": "string"}, "CompanyEmail": {"type": "string"}, "TxnAmount": {"type": "double"}, "TxnDueDays": {"type": "integer"}, "intuit_userid": {"type": "String"}, "sendCompanyEmail": {"value": "false", "type": "String"}, "TxnBalanceAmount": {"type": "double"}, "CompanyName": {"type": "string"}, "TxnDueDate": {"type": "string"}, "DocNumber": {"type": "string"}, "Id": {"type": "String"}, "createTask": {"value": "true", "type": "String"}, "intuit_realmid": {"type": "String"}}, "user_variables": {"reminder:createTask": {"parameters": {"Assignee": {"fieldValue": []}, "CloseTask": {"fieldValue": ["txn_paid"]}, "TaskName": {"fieldValue": ["Review [[DocNumber]] for Amount [[TxnAmount]] and Balance [[TxnDueDate]]"]}, "ProjectType": {"fieldValue": ["QB_INVOICE_REMINDER"]}, "TaskType": {"fieldValue": ["QB_BILL"]}}, "selected": true}, "reminder:sendCompanyEmail": {"parameters": {"CC": {"fieldValue": []}, "IsEmail": {"fieldValue": ["true"]}, "BCC": {"fieldValue": []}, "Message": {"fieldValue": ["[[Invoice Number]] needs your attention"]}, "SendTo": {"fieldValue": ["<EMAIL>"]}, "Subject": {"fieldValue": ["Review Bill [[DocNumber]]"]}}, "selected": false}, "customStartEvent": {"parameters": {}, "selected": true}}, "user_meta_data": {"intuit_was_locale": "en_US"}}, "dmn_placeholder_values": {"rule_line_variables": [{"conditionalExpression": "GT 500", "parameterName": "TxnAmount"}, {"conditionalExpression": "AF 1", "parameterName": "TxnDueDays"}]}}