{"user_variables": {"waitForTimerToElapse1_invoiceApproval_companyId_uuid": {"selected": true, "parameters": {"entityOperationDummy": {"fieldValue": ["create,update"]}}}, "createTask": {"selected": "true", "parameters": {"Assignee": {"fieldType": "string", "fieldValue": ["9130350413749026"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "TaskName": {"fieldType": "string", "fieldValue": ["Review [[DocNumber]] for Amount [[TxnAmount]] and Balance [[TxnBalanceAmount]]"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "TaskType": {"fieldType": "string", "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}, "CloseTask": {"fieldType": "string", "fieldValue": ["txn_paid"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true, "possibleFieldValues": ["txn_paid", "txn_sent", "close_manually"]}, "ProjectType": {"fieldType": "string", "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}}}, "reminder:sendApprovalEmail_invoiceApproval_companyId_uuid": {"selected": "false", "parameters": {"CC": {"fieldType": "string", "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "BCC": {"fieldType": "string", "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "To": {"fieldType": "string", "fieldValue": ["[[CompanyEmail]]"], "multiSelect": false, "configurable": true, "requiredByUI": true, "handlerFieldName": "To", "requiredByHandler": true}, "IsEmail": {"fieldType": "boolean", "fieldValue": ["true"], "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}, "Message": {"fieldType": "string", "fieldValue": ["Hi username, \n[[<PERSON><PERSON><PERSON><PERSON>]] needs your attention. Please take a look at the invoice and complete any outstanding tasks. \n\nThanks,"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "Subject": {"fieldType": "string", "fieldValue": ["Review [[DocNumber]]"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}}}, "sendApprovalEmail_invoiceApproval_companyId_uuid": {"selected": "false", "parameters": {"CC": {"fieldType": "string", "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "BCC": {"fieldType": "string", "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "To": {"fieldType": "string", "fieldValue": ["[[CompanyEmail]]"], "multiSelect": false, "configurable": true, "requiredByUI": true, "handlerFieldName": "To", "requiredByHandler": true}, "IsEmail": {"fieldType": "boolean", "fieldValue": ["true"], "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}, "Message": {"fieldType": "string", "fieldValue": ["Hi username, \n[[<PERSON><PERSON><PERSON><PERSON>]] needs your attention. Please take a look at the invoice and complete any outstanding tasks. \n\nThanks,"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "Subject": {"fieldType": "string", "fieldValue": ["Review [[DocNumber]]"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}}}, "sendExternalEmail": {"selected": "true", "parameters": {"CC": {"fieldType": "string", "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "BCC": {"fieldType": "string", "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "SendTo": {"fieldType": "string", "fieldValue": ["<EMAIL>"], "multiSelect": false, "configurable": true, "requiredByUI": true, "handlerFieldName": "To", "requiredByHandler": true}, "IsEmail": {"fieldType": "boolean", "fieldValue": ["true"], "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}, "Message": {"fieldType": "string", "fieldValue": ["SAMPLE MESSAGESAMPLE sample"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "Subject": {"fieldType": "string", "fieldValue": ["Subject placeholder"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "SendAttachment": {"fieldType": "boolean", "fieldValue": ["true"], "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}}}, "sendPushNotification": {"selected": "false", "parameters": {"SendTo": {"fieldType": "string", "multiSelect": false, "configurable": true, "requiredByUI": true, "handlerFieldName": "To", "requiredByHandler": true}, "Message": {"fieldType": "string", "fieldValue": ["Go to QuickBooks to view it."], "multiSelect": false, "configurable": false, "requiredByUI": true, "requiredByHandler": true}, "Subject": {"fieldType": "string", "fieldValue": ["An Invoice needs your attention"], "multiSelect": false, "configurable": false, "requiredByUI": true, "requiredByHandler": true}, "IsMobile": {"fieldType": "boolean", "fieldValue": ["true"], "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}, "NotificationAction": {"fieldType": "string", "fieldValue": ["qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]"], "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}}}, "recurrenceRule": "{\"recurType\":\"WEEKLY\",\"interval\":1,\"daysOfWeek\":[\"THURSDAY\"],\"startDate\":{\"secondOfDay\":0,\"minuteOfDay\":0,\"centuryOfEra\":20,\"yearOfEra\":2022,\"yearOfCentury\":22,\"weekyear\":2022,\"monthOfYear\":2,\"weekOfWeekyear\":8,\"hourOfDay\":0,\"minuteOfHour\":0,\"secondOfMinute\":0,\"millisOfSecond\":0,\"year\":2022,\"dayOfMonth\":22,\"millisOfDay\":0,\"dayOfWeek\":2,\"era\":1,\"dayOfYear\":53,\"chronology\":{\"zone\":{\"uncachedZone\":{\"cachable\":true,\"fixed\":false,\"id\":\"Asia/Kolkata\"},\"fixed\":false,\"id\":\"Asia/Kolkata\"}},\"zone\":{\"uncachedZone\":{\"cachable\":true,\"fixed\":false,\"id\":\"Asia/Kolkata\"},\"fixed\":false,\"id\":\"Asia/Kolkata\"},\"millis\":1645468200000,\"afterNow\":false,\"beforeNow\":true,\"equalNow\":false},\"active\":true,\"$sdk_validated\":true}"}, "process_variables": {"Id": {"type": "String", "value": ""}, "TxnDate": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "TxnDate", "requiredByHandler": true}, "DocNumber": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "DocNumber", "requiredByHandler": true}, "SyncToken": {"type": "String", "value": ""}, "TxnAmount": {"fieldType": "double", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "TxnAmount", "requiredByHandler": true}, "TxnDueDate": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "TxnDueDate", "requiredByHandler": true}, "createTask": {"type": "String", "value": "true"}, "entityType": {"type": "String", "value": ""}, "CompanyName": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "CompanyName", "requiredByHandler": true}, "CompanyEmail": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "CompanyEmail", "requiredByHandler": true}, "CustomerName": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "CustomerName", "requiredByHandler": true}, "CustomerEmail": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "CustomerEmail", "requiredByHandler": true}, "intuit_userid": {"type": "String", "value": ""}, "intuit_realmid": {"type": "String", "value": ""}, "TxnBalanceAmount": {"fieldType": "double", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "TxnBalanceAmount", "requiredByHandler": true}, "entityChangeType": {"type": "String", "value": ""}, "sendCompanyEmail": {"type": "String", "value": "null"}, "sendExternalEmail": {"type": "String", "value": "true"}, "sendPushNotification": {"type": "String", "value": "null"}}}