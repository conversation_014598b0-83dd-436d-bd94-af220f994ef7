{"user_meta_data": {"intuit_was_locale": "en_US"}, "process_variables": {"entityChangeType": {"type": "String"}, "TxnDate": {"type": "string"}, "CompanyEmail": {"type": "string"}, "TxnAmount": {"type": "double"}, "intuit_userid": {"type": "String"}, "TxnDueDays": {"type": "integer"}, "sendCompanyEmail": {"type": "String", "value": "false"}, "TxnBalanceAmount": {"type": "double"}, "CompanyName": {"type": "string"}, "TxnDueDate": {"type": "string"}, "CustomerEmail": {"type": "string"}, "DocNumber": {"type": "string"}, "Id": {"type": "String"}, "CustomerName": {"type": "string"}, "createTask": {"type": "String", "value": "true"}, "intuit_realmid": {"type": "String"}}, "user_variables": {"reminder:createTask": {"parameters": {"Assignee": {"fieldValue": []}, "CloseTask": {"fieldValue": ["txn_paid"]}, "TaskName": {"fieldValue": ["Review [[DocNumber]] for Amount [[TxnAmount]] and Balance [[TxnDueDate]]"]}, "ProjectType": {"fieldValue": ["QB_INVOICE_REMINDER"]}, "TaskType": {"fieldValue": ["QB_INVOICE"]}}, "selected": true}, "reminder:sendCompanyEmail": {"parameters": {"CC": {"fieldValue": []}, "IsEmail": {"fieldValue": ["true"]}, "BCC": {"fieldValue": []}, "Message": {"fieldValue": ["[[<PERSON><PERSON><PERSON><PERSON>]] needs your attention"]}, "SendTo": {"fieldValue": ["<EMAIL>"]}, "Subject": {"fieldValue": ["Review Invoice [[DocNumber]]"]}}, "selected": false}, "customStartEvent": {"parameters": {"entityOperation": {"fieldValue": ["create,update"]}}, "selected": true}}, "dmn_placeholder_values": {"rule_line_variables": [{"conditionalExpression": "GT 500", "parameterName": "TxnAmount"}, {"conditionalExpression": "AF 1", "parameterName": "TxnDueDays"}]}}