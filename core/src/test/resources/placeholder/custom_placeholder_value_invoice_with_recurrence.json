{"user_meta_data": {"intuit_was_locale": "en_US"}, "process_variables": {"entityChangeType": {"type": "String"}, "TxnDate": {"type": "string"}, "CompanyEmail": {"type": "string"}, "TxnAmount": {"type": "double"}, "intuit_userid": {"type": "String"}, "TxnDueDays": {"type": "integer"}, "sendCompanyEmail": {"type": "String", "value": "false"}, "TxnBalanceAmount": {"type": "double"}, "CompanyName": {"type": "string"}, "TxnDueDate": {"type": "string"}, "CustomerEmail": {"type": "string"}, "DocNumber": {"type": "string"}, "Id": {"type": "String"}, "CustomerName": {"type": "string"}, "createTask": {"type": "String", "value": "true"}, "intuit_realmid": {"type": "String"}}, "user_variables": {"recurrenceRule": "{\"active\":true,\"interval\":1,\"recurType\":\"MONTHLY\",\"startDate\":{\"era\":1,\"year\":2022,\"zone\":{\"ID\":\"Asia/Kolkata\",\"fixed\":false,\"uncachedZone\":{\"ID\":\"Asia/Kolkata\"}},\"millis\":1647973800000,\"afterNow\":false,\"equalNow\":false,\"weekyear\":2022,\"beforeNow\":true,\"dayOfWeek\":3,\"dayOfYear\":82,\"hourOfDay\":0,\"yearOfEra\":2022,\"chronology\":{\"zone\":{\"ID\":\"Asia/Kolkata\",\"fixed\":false,\"uncachedZone\":{\"ID\":\"Asia/Kolkata\"}}},\"dayOfMonth\":23,\"millisOfDay\":0,\"minuteOfDay\":0,\"monthOfYear\":3,\"secondOfDay\":0,\"centuryOfEra\":20,\"minuteOfHour\":0,\"yearOfCentury\":22,\"millisOfSecond\":0,\"secondOfMinute\":0,\"weekOfWeekyear\":12},\"daysOfWeek\":[\"WEDNESDAY\"],\"weekOfMonth\":\"FOURTH\",\"recurrenceTime\":{\"hours\":7,\"minutes\":15}}", "reminder:createTask": {"parameters": {"Assignee": {"fieldValue": []}, "CloseTask": {"fieldValue": ["txn_paid"]}, "TaskName": {"fieldValue": ["Review [[DocNumber]] for Amount [[TxnAmount]] and Balance [[TxnDueDate]]"]}, "ProjectType": {"fieldValue": ["QB_INVOICE_REMINDER"]}, "TaskType": {"fieldValue": ["QB_INVOICE"]}}, "selected": true}, "reminder:sendCompanyEmail": {"parameters": {"CC": {"fieldValue": []}, "IsEmail": {"fieldValue": ["true"]}, "BCC": {"fieldValue": []}, "Message": {"fieldValue": ["[[<PERSON><PERSON><PERSON><PERSON>]] needs your attention"]}, "SendTo": {"fieldValue": ["<EMAIL>"]}, "Subject": {"fieldValue": ["Review Invoice [[DocNumber]]"]}}, "selected": false}, "customStartEvent": {"parameters": {"entityOperation": {"fieldValue": ["create,update"]}}, "selected": true}}, "dmn_placeholder_values": {"rule_line_variables": [{"conditionalExpression": "GT 500", "parameterName": "TxnAmount"}, {"conditionalExpression": "AF 1", "parameterName": "TxnDueDays"}]}}