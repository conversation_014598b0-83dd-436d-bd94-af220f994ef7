{"user_variables": {"createTask_bankdepositreminder": {"selected": "true", "parameters": {"Assignee": {"fieldType": "string", "fieldValue": ["****************"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "TaskName": {"fieldType": "string", "fieldValue": ["Review [[DocNumber]] for Amount [[TxnAmount]] and Balance [[TxnBalanceAmount]]-URGENT"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true}, "TaskType": {"fieldType": "string", "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}, "CloseTask": {"fieldType": "string", "fieldValue": ["txn_paid"], "multiSelect": false, "configurable": true, "requiredByUI": true, "requiredByHandler": true, "possibleFieldValues": ["txn_paid", "txn_sent", "close_manually"]}, "ProjectType": {"fieldType": "string", "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}}}, "sendPushNotification_bankdepositreminder": {"selected": "false", "parameters": {"SendTo": {"fieldType": "string", "multiSelect": false, "configurable": true, "requiredByUI": true, "handlerFieldName": "To", "requiredByHandler": true}, "Message": {"fieldType": "string", "fieldValue": ["Go to QuickBooks to view it.[URGENT]"], "multiSelect": false, "configurable": false, "requiredByUI": true, "requiredByHandler": true}, "Subject": {"fieldType": "string", "fieldValue": ["An Invoice needs your attention"], "multiSelect": false, "configurable": false, "requiredByUI": true, "requiredByHandler": true}, "IsMobile": {"fieldType": "boolean", "fieldValue": ["true"], "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}, "NotificationAction": {"fieldType": "string", "fieldValue": ["qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]"], "multiSelect": false, "configurable": false, "requiredByUI": false, "requiredByHandler": true}}}}, "process_variables": {"Id": {"type": "String", "value": ""}, "TxnDate": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "TxnDate", "requiredByHandler": true}, "DocNumber": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "DocNumber", "requiredByHandler": true}, "SyncToken": {"type": "String", "value": ""}, "TxnAmount": {"fieldType": "double", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "TxnAmount", "requiredByHandler": true}, "TxnDueDate": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "TxnDueDate", "requiredByHandler": true}, "createTask": {"type": "String", "value": "true"}, "entityType": {"type": "String", "value": ""}, "CompanyName": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "CompanyName", "requiredByHandler": true}, "CompanyEmail": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "CompanyEmail", "requiredByHandler": true}, "CustomerName": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "CustomerName", "requiredByHandler": true}, "CustomerEmail": {"fieldType": "string", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "CustomerEmail", "requiredByHandler": true}, "intuit_userid": {"type": "String", "value": ""}, "intuit_realmid": {"type": "String", "value": ""}, "TxnBalanceAmount": {"fieldType": "double", "valueType": "PROCESS_VARIABLE", "multiSelect": false, "configurable": false, "requiredByUI": false, "handlerFieldName": "TxnBalanceAmount", "requiredByHandler": true}, "entityChangeType": {"type": "String", "value": ""}, "sendCompanyEmail": {"type": "String", "value": "null"}, "sendExternalEmail": {"type": "String", "value": "true"}, "sendPushNotification": {"type": "String", "value": "null"}}}