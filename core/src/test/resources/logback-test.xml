<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="SERVER_FILE">
        <file>logs/DemoApplication.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/workflowAutomationApplication-%i.log.gz</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>5</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>300MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>
                <pattern>
                [%date{"yyyy-MM-dd'T'HH:mm:ss,SSSZ"}]-[%-5level]-[tid=%X{tid:-NA} , clusterId=%X{clusterId:-NA}]-[%logger{0}]-[%-4.-4line] %msg %ex{full}%n
            </pattern>
            </pattern>
        </encoder>
    </appender>

    <appender class="ch.qos.logback.core.ConsoleAppender" name="SERVER_CONSOLE">
        <withJansi>true</withJansi>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                <pattern>
                [%date{"yyyy-MM-dd'T'HH:mm:ss,SSSZ"}]-[%-5level]-[tid=%X{tid:-NA} , clusterId=%X{clusterId:-NA}]-[%logger{0}]-[%-4.-4line] %msg %ex{full}%n
            </pattern>
            </pattern>
        </encoder>
    </appender>

    <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="OILFILE">
        <file>OILLogs/applicationOIL.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/OILLogs/applicationOIL_%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>200MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d %-4relative [%thread] %-5level %logger{35} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender class="ch.qos.logback.classic.AsyncAppender" name="OIL_CONSOLE">
        <appender-ref ref="SERVER_CONSOLE"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender class="ch.qos.logback.classic.AsyncAppender" name="SERVER_FILE_ASYNC">
        <appender-ref ref="SERVER_FILE"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <logger additivity="false" level="DEBUG" name="com.intuit.appintgwkflw.wkflautomate">
        <appender-ref ref="SERVER_CONSOLE"/>
        <appender-ref ref="SERVER_FILE_ASYNC"/>
    </logger>

    <logger name="com.intuit.platform.jsk" level="WARN">
        <appender-ref ref="SERVER_CONSOLE"/>
        <appender-ref ref="SERVER_FILE_ASYNC"/>
    </logger>

    <logger level="INFO" name="com.intuit.platform.oil">
        <appender-ref ref="OILFILE"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="SERVER_CONSOLE"/>
        <appender-ref ref="SERVER_FILE_ASYNC"/>
    </root>
</configuration>
