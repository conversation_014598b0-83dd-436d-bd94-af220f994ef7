{"eventHeaders": {"workflow": "customNotification", "entityType": "Customer", "entityChangeType": "Create"}, "entity": {"Customer": {"CurrentBalance": 20, "CompanyName": "Intuit", "Classification": "", "Currency": "USD", "BillWithParent": "NO", "DisplayName": "Manish", "PreferredDeliveryMethod": "NONE", "Job": "NO", "CreateTime": "2023-01-16T23:41:09-08:00", "LastUpdatedTime": "2023-01-17T09:05:27-08:00", "CompanyEmail": "<EMAIL>", "Active": "YES", "BalanceWithJobs": "20", "Id": "22", "Domain": "QBO", "Taxable": "NO"}}}