<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_09ch9f6" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.3.1">
  <bpmn:process id="InvoiceApproval" name="Invice Approval" isExecutable="true" camunda:versionTag="1">
    <bpmn:extensionElements>
      <camunda:properties>
        <camunda:property name="description" value="&#34;Invoice approval&#34;" />
      </camunda:properties>
    </bpmn:extensionElements>
    <bpmn:sendTask id="sendApprovalEmail_invoiceApproval" name="Send approval email" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "duzz/send-email",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["<EMAIL>"],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_ID",
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue" : ["Invoice [[QB:DocNumber]] requires approval"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message" : {
                        "fieldValue" : ["Invoice for [[QB:CustomerName]] for $[[QB:Amount]] requires approval.\nRegards"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "helpVariables": "QBOCompanyName",
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          <camunda:outputParameter name="outputRsponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceToApproval_invoiceApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0e5o342</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:receiveTask id="waitForApproval1_invoiceApproval" name="Wait for approval" messageRef="Message_02ejj4p">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "invoice",
                        "eventType": "update"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails" />
          <camunda:inputParameter name="workflowStepId">3_uuid</camunda:inputParameter>
          <camunda:inputParameter name="currentStepDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0qvu7rx</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0vidx1u</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:boundaryEvent id="waitForTimerToElapse1_invoiceApproval" attachedToRef="waitForApproval1_invoiceApproval">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
          <camunda:property name="workflowStepId" value="4_uuid" />
          <camunda:property name="parameterDetails" value="{  &#34;waitTime&#34;: {     &#34;fieldValue&#34; : [&#34;5&#34;],     &#34;configurable&#34; : true,     &#34;actionByUI&#34; : null,     &#34;requiredByhandler&#34; : false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;integer&#34;   }}" />
          <camunda:property name="currentStepDetails" value="{&#34;required&#34;: true}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_0nfqqec</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${approvalWaitTime}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="sequenceSendReminder_invoiceApproval" name="Reminder action chosen by user" sourceRef="evaluateUserDefinedAction_invoiceApproval" targetRef="sendReminderEmail_invoiceApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${userDefined.sendApprovalReminder == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sendTask id="sendReminderEmail_invoiceApproval" name="Send reminder" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:class="CamundaTaskDelegate">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "duzz/send-email",
                        "actionName": "executeDummyAction",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["Admins"],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_ID",
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message" : {
                        "fieldValue" : ["blah blah link blah"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": true,
                        "hepVariables": "QBOCompanyName",
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "uiVisibility": true }</camunda:inputParameter>
          <camunda:outputParameter name="outputRsponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceSendReminder_invoiceApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0f9ufa8</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sequenceFlow id="SequenceFlow_0f9ufa8" sourceRef="sendReminderEmail_invoiceApproval" targetRef="waitForApproval2_invoiceApproval" />
    <bpmn:receiveTask id="waitForApproval2_invoiceApproval" name="Wait for approval" messageRef="Message_02ejj4p">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "invoice",
                        "eventType": "delete"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails" />
          <camunda:inputParameter name="workflowStepId">5_uuid</camunda:inputParameter>
          <camunda:inputParameter name="currentStepDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0f9ufa8</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1t8fodz</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="evaluateUserDefinedAction_invoiceApproval">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;UI&#34;}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
          <camunda:property name="parameterDetails" value="&#34;To&#34;: {                         &#34;fieldValue&#34; : [&#34;Admins&#34;],                         &#34;configurable&#34; : true,                         &#34;actionByUI&#34; : &#34;GET_ADMINS_ID&#34;,                         &#34;requiredByHandler&#34; : true,                         &#34;requiredByUI&#34;: true,                         &#34;multiSelect&#34;: true,                         &#34;fieldType&#34;: &#34;string&#34;                         },                         &#34;Subject&#34;: {                         &#34;fieldValue&#34; : [&#34;Hey you ahve an invoice to approve&#34;],                         &#34;configurable&#34; : true,                         &#34;actionByUI&#34; : null,                         &#34;requiredByhandler&#34; : true,                         &#34;requiredByUI&#34;: true,                         &#34;multiSelect&#34;: false,                         &#34;fieldType&#34;: &#34;string&#34;                         },                         &#34;Message&#34; : {                         &#34;fieldValue&#34; : [&#34;blah blah link blah&#34;],                         &#34;configurable&#34; : true,                         &#34;actionByUI&#34; : null,                         &#34;requiredByhandler&#34; : true,                         &#34;requiredByUI&#34;: true,                         &#34;helpVariables&#34;: &#34;QBOCompanyName&#34;,                         &#34;multiSelect&#34;: false,                         &#34;fieldType&#34;: &#34;string&#34;                         }                         }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0nfqqec</bpmn:incoming>
      <bpmn:outgoing>sequenceSendReminder_invoiceApproval</bpmn:outgoing>
      <bpmn:outgoing>sequenceAutoUpdate_invoiceApproval</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0nfqqec" sourceRef="waitForTimerToElapse1_invoiceApproval" targetRef="evaluateUserDefinedAction_invoiceApproval" />
    <bpmn:sequenceFlow id="sequenceAutoUpdate_invoiceApproval" sourceRef="evaluateUserDefinedAction_invoiceApproval" targetRef="autoUpdateAsApproved_invoiceApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="autoUpdateAsApproved_invoiceApproval" name="Auto approve invoice status" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "actionName": "autoUpdateInvoiceStatus",
                        "handlerId": "5656",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{
                        "required": true
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
                        "invoiceId": {
                        "fieldValue" : ["12345"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "invoiceStatus": {
                        "fieldValue" : ["approved"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "displayName": {
                        "fieldValue" : ["auto approve invoice"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : false,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceAutoUpdate_invoiceApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_10ahpqh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="end3_invoiceApproval" name="Invoice auto approved because of time out">
      <bpmn:incoming>SequenceFlow_10ahpqh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:boundaryEvent id="waitForTimerToElapse2_invoiceApproval" attachedToRef="waitForApproval2_invoiceApproval">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: false}" />
          <camunda:property name="workflowStepId" value="6_uuid" />
          <camunda:property name="parameterDetails" value="{  &#34;waitTime&#34;: {     &#34;fieldValue&#34; : [&#34;30&#34;],    &#34;configurable&#34; : true,     &#34;actionByUI&#34; : null,     &#34;requiredByhandler&#34; : false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;integer&#34;   }}" />
          <camunda:property name="currentStepDetails" value="{&#34;required&#34;: true}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_reminderTimerElapsed</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${userDefined.approvalReminderWaitTime}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="SequenceFlow_reminderTimerElapsed" sourceRef="waitForTimerToElapse2_invoiceApproval" targetRef="autoRejectInvoice_invoiceApproval" />
    <bpmn:endEvent id="end4_invoiceApproval" name="Invoice status reset">
      <bpmn:incoming>SequenceFlow_0041dgc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0041dgc" sourceRef="autoRejectInvoice_invoiceApproval" targetRef="end4_invoiceApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_10ahpqh" sourceRef="autoUpdateAsApproved_invoiceApproval" targetRef="end3_invoiceApproval" />
    <bpmn:exclusiveGateway id="exclusiveGateway_invoiceApproval" name="approved?" camunda:asyncBefore="true" camunda:asyncAfter="true">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{                         &#34;taskHandler&#34;: &#34;appconnect&#34;,                         &#34;handlerId&#34;: &#34;1234&#34;,                         &#34;handlerName&#34;: &#34;sendEmail&#34;                         }" />
          <camunda:property name="parameterDetails" value="{                         &#34;invoice.status&#34;: {                         &#34;fieldValue&#34; : [&#34;approved&#34;],                         &#34;configurable&#34; : false,                         &#34;actionByUI&#34; : null,                         &#34;requiredByhandler&#34; : false,                         &#34;requiredByUI&#34;: false,                         &#34;multiSelect&#34;: false,                         &#34;fieldType&#34;: &#34;string&#34;                         }}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0vidx1u</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1t8fodz</bpmn:incoming>
      <bpmn:outgoing>sequenceApproved_invoiceApproval</bpmn:outgoing>
      <bpmn:outgoing>sequenceRejected_invoiceApproval</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0vidx1u" sourceRef="waitForApproval1_invoiceApproval" targetRef="exclusiveGateway_invoiceApproval" />
    <bpmn:sequenceFlow id="sequenceApproved_invoiceApproval" name="yes" sourceRef="exclusiveGateway_invoiceApproval" targetRef="sendApproveNotification_invoiceApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${invoice.status == approved}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="sequenceRejected_invoiceApproval" name="no" sourceRef="exclusiveGateway_invoiceApproval" targetRef="sendRejectNotification_invoiceApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:startEvent id="newInvoiceCreated_invoiceApproval" name="Invoice create event" camunda:asyncAfter="true">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;recordType&#34;: &#34;invoice&#34;, &#34;eventType&#34;: &#34;create&#34;}" />
          <camunda:property name="stepDetails" value="{     &#34;newInvoiceCreated_invoiceApproval&#34;: [         &#34;newInvoiceCreated_invoiceApproval&#34;,         &#34;invoiceApprovalDecision_invoiceApproval_companyId_uuid&#34;,         &#34;sendApprovalEmail_invoiceApproval&#34;,         &#34;sendApprovalNotification_invoiceApproval&#34;,         &#34;waitForDeleteOrVoid_invoiceApproval&#34;     ],     &#34;waitForApproval1_invoiceApproval&#34;: [         &#34;waitForApproval1_invoiceApproval&#34;,         &#34;exclusiveGateway_invoiceApproval&#34;,         &#34;sequenceApproved_invoiceApproval&#34;,         &#34;sequenceRejected_invoiceApproval&#34;,         &#34;sendApproveNotification_invoiceApproval&#34;,         &#34;sendRejectNotification_invoiceApproval&#34;     ],     &#34;waitForTimerToElapse1_invoiceApproval&#34;: [         &#34;waitForTimerToElapse1_invoiceApproval&#34;,         &#34;evaluateUserDefinedAction_invoiceApproval&#34;,         &#34;sequenceAutoUpdate_invoiceApproval&#34;,         &#34;sequenceSendReminder_invoiceApproval&#34;,         &#34;sendReminderEmail_invoiceApproval&#34;,         &#34;autoUpdateAsApproved_invoiceApproval&#34;     ],     &#34;waitForApproval2_invoiceApproval&#34;: [         &#34;waitForApproval2_invoiceApproval&#34;,         &#34;exclusiveGateway_invoiceApproval&#34;,         &#34;sequenceApproved_invoiceApproval&#34;,         &#34;sequenceRejected_invoiceApproval&#34;,         &#34;sendApproveNotification_invoiceApproval&#34;,         &#34;sendRejectNotification_invoiceApproval&#34;     ],     &#34;waitForTimerToElapse2_invoiceApproval&#34;: [         &#34;waitForTimerToElapse2_invoiceApproval&#34;,         &#34;autoRejectInvoice_invoiceApproval&#34;     ] }" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="workflowStepId" value="1_uuid" />
          <camunda:property name="currentStepDetails" value="{&#34;required&#34;: true}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_09y5vl1</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0tc7nq5</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sendTask id="sendApprovalNotification_invoiceApproval" name="Send approval notification" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "5678",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["Admins"],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_ID",
                        "requiredByhandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          <camunda:outputParameter name="outputRsponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceToNotify_invoiceApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ke2v55</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sequenceFlow id="SequenceFlow_1t8fodz" sourceRef="waitForApproval2_invoiceApproval" targetRef="exclusiveGateway_invoiceApproval" />
    <bpmn:endEvent id="end5_invoiceApproval" name="Invoice approved">
      <bpmn:incoming>SequenceFlow_0o7eg9h</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0o7eg9h" sourceRef="sendRejectNotification_invoiceApproval" targetRef="end5_invoiceApproval" />
    <bpmn:endEvent id="end6_invoiceApproval" name="Invoice rejected">
      <bpmn:incoming>SequenceFlow_0v85vpc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0v85vpc" sourceRef="sendApproveNotification_invoiceApproval" targetRef="end6_invoiceApproval" />
    <bpmn:serviceTask id="autoRejectInvoice_invoiceApproval" name="Auto reject invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:class="CamundaTaskDelegate">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "actionName": "autoRejectInvoiceStatus",
                        "handlerId": "5555",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{
                        "required": true
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
                        "invoiceId": {
                        "fieldValue" : ["12345"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "invoiceStatus": {
                        "fieldValue" : ["approved"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "displayName": {
                        "fieldValue" : ["auto approve invoice"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : false,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_reminderTimerElapsed</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0041dgc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="sendRejectNotification_invoiceApproval" name="Send notification to creator of invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:class="CamundaTaskDelegate">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "9876",
                        "actionName":"sendNotificationToAuthor",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["userId"],
                        "configurable" : true,
                        "actionByUI" : "GET_AUTHOR_ID",
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        }
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="outputRsponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceRejected_invoiceApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0o7eg9h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="sendApproveNotification_invoiceApproval" name="Send notification to creator of invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:class="CamundaTaskDelegate">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "5432",
                        "actionName": "sendNotificationToAuthor",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue": ["creator"],
                        "configurable": true,
                        "actionByUI": "GET_AUTHOR_ID",
                        "requiredByhandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "Subject": {
                        "fieldValue": [
                        "hey your invoice rejected"
                        ],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByhandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        }
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="outputRsponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceApproved_invoiceApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0v85vpc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0qvu7rx" sourceRef="parallelGateway_invoiceApproval" targetRef="waitForApproval1_invoiceApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_0ke2v55" sourceRef="sendApprovalNotification_invoiceApproval" targetRef="parallelGateway_invoiceApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_0e5o342" sourceRef="sendApprovalEmail_invoiceApproval" targetRef="parallelGateway_invoiceApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_18srail" sourceRef="waitForDeleteOrVoid_invoiceApproval" targetRef="end2_invoiceApproval" />
    <bpmn:endEvent id="end2_invoiceApproval" name="Invoice deleted or voided">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_18srail</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:receiveTask id="waitForDeleteOrVoid_invoiceApproval" name="Wait for delete or void">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "invoice",
                        "eventType": "delete"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails" />
          <camunda:inputParameter name="workflowStepId">2_uuid</camunda:inputParameter>
          <camunda:inputParameter name="currentStepDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceToWait1_invoiceApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_18srail</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:endEvent id="end1_invoiceApproval" name="No rules matched">
      <bpmn:incoming>SequenceFlow_0skvj0g</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:parallelGateway id="parallelGateway_invoiceApproval">
      <bpmn:incoming>SequenceFlow_0ke2v55</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0e5o342</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0qvu7rx</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:subProcess id="SubProcess_0sx7c5s" name="Global Error Handler" triggeredByEvent="true">
      <bpmn:startEvent id="StartEvent_0u40fcr" name="Start">
        <bpmn:outgoing>SequenceFlow_0aeokfr</bpmn:outgoing>
        <bpmn:errorEventDefinition id="ErrorEventDefinition_1cns220" camunda:errorCodeVariable="bpmerror" />
      </bpmn:startEvent>
      <bpmn:endEvent id="EndEvent_1rhv75h" name="end">
        <bpmn:incoming>SequenceFlow_1ghw453</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask id="ServiceTask_0qc5h4h" name="Handle Error" camunda:class="ErrorHandlerDelegate">
        <bpmn:incoming>SequenceFlow_0aeokfr</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_1ghw453</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="SequenceFlow_1ghw453" sourceRef="ServiceTask_0qc5h4h" targetRef="EndEvent_1rhv75h" />
      <bpmn:sequenceFlow id="SequenceFlow_0aeokfr" sourceRef="StartEvent_0u40fcr" targetRef="ServiceTask_0qc5h4h" />
    </bpmn:subProcess>
    <bpmn:businessRuleTask id="invoiceApprovalDecision_invoiceApproval_companyId_uuid" name="Invoice approval rule evaluation" camunda:resultVariable="decision" camunda:decisionRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" camunda:mapDecisionResult="singleResult">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_09y5vl1</bpmn:incoming>
      <bpmn:outgoing>sequenceToApproval_invoiceApproval</bpmn:outgoing>
      <bpmn:outgoing>sequenceToNotify_invoiceApproval</bpmn:outgoing>
      <bpmn:outgoing>sequenceToWait1_invoiceApproval</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0skvj0g</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sequenceFlow id="SequenceFlow_09y5vl1" sourceRef="newInvoiceCreated_invoiceApproval" targetRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" />
    <bpmn:sequenceFlow id="sequenceToApproval_invoiceApproval" sourceRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" targetRef="sendApprovalEmail_invoiceApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.sendApprovalEmail == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="sequenceToNotify_invoiceApproval" sourceRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" targetRef="sendApprovalNotification_invoiceApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.sendApprovalNotification == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="sequenceToWait1_invoiceApproval" sourceRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" targetRef="waitForDeleteOrVoid_invoiceApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(decision.sendApprovalEmail == true) || (decision.sendApprovalNotification == true)}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0skvj0g" sourceRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" targetRef="end1_invoiceApproval" />
    <bpmn:task id="Task_0mc1xf4">
      <bpmn:incoming>SequenceFlow_0tc7nq5</bpmn:incoming>
    </bpmn:task>
    <bpmn:sequenceFlow id="SequenceFlow_0tc7nq5" sourceRef="newInvoiceCreated_invoiceApproval" targetRef="Task_0mc1xf4" />
  </bpmn:process>
  <bpmn:message id="Message_02ejj4p" name="Message_32llkbg" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="InvoiceApproval">
      <bpmndi:BPMNShape id="BusinessRuleTask_0vhb5x8_di">
        <dc:Bounds x="323" y="440" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_16erw3l_di">
        <di:waypoint x="423" y="453" />
        <di:waypoint x="498" y="453" />
        <di:waypoint x="498" y="438" />
        <di:waypoint x="572" y="438" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SendTask_0cbehcx_di" bpmnElement="sendApprovalEmail_invoiceApproval">
        <dc:Bounds x="572" y="408" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ReceiveTask_0u4i7br_di" bpmnElement="waitForApproval1_invoiceApproval">
        <dc:Bounds x="877" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BoundaryEvent_0nb8g8w_di" bpmnElement="waitForTimerToElapse1_invoiceApproval">
        <dc:Bounds x="903" y="542" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="811" y="347" width="4" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_109yat4_di" bpmnElement="sequenceSendReminder_invoiceApproval">
        <di:waypoint x="946" y="672" />
        <di:waypoint x="1044" y="672" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="945" y="641" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SendTask_1g3ea5h_di" bpmnElement="sendReminderEmail_invoiceApproval">
        <dc:Bounds x="1044" y="632" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0f9ufa8_di" bpmnElement="SequenceFlow_0f9ufa8">
        <di:waypoint x="1145" y="672" />
        <di:waypoint x="1192" y="672" />
        <di:waypoint x="1192" y="567" />
        <di:waypoint x="1280" y="567" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ReceiveTask_10ztdcz_di" bpmnElement="waitForApproval2_invoiceApproval">
        <dc:Bounds x="1282" y="505" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_026vdsv_di" bpmnElement="evaluateUserDefinedAction_invoiceApproval" isMarkerVisible="true">
        <dc:Bounds x="896" y="647" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0nfqqec_di" bpmnElement="SequenceFlow_0nfqqec">
        <di:waypoint x="921" y="578" />
        <di:waypoint x="921" y="647" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sz88v2_di" bpmnElement="sequenceAutoUpdate_invoiceApproval">
        <di:waypoint x="921" y="697" />
        <di:waypoint x="921" y="748" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_0fvublp_di" bpmnElement="autoUpdateAsApproved_invoiceApproval">
        <dc:Bounds x="871" y="748" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_00yp4tl_di" bpmnElement="end3_invoiceApproval">
        <dc:Bounds x="634" y="770" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="614" y="813" width="78" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BoundaryEvent_1gb6h60_di" bpmnElement="waitForTimerToElapse2_invoiceApproval">
        <dc:Bounds x="1316" y="567" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_09zaj8t_di" bpmnElement="SequenceFlow_reminderTimerElapsed">
        <di:waypoint x="1334" y="603" />
        <di:waypoint x="1334" y="697" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_09o1vpy_di" bpmnElement="end4_invoiceApproval">
        <dc:Bounds x="1316" y="806" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1302" y="849" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0041dgc_di" bpmnElement="SequenceFlow_0041dgc">
        <di:waypoint x="1334" y="777" />
        <di:waypoint x="1334" y="806" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_10ahpqh_di" bpmnElement="SequenceFlow_10ahpqh">
        <di:waypoint x="871" y="788" />
        <di:waypoint x="670" y="788" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_1gey5dp_di" bpmnElement="exclusiveGateway_invoiceApproval" isMarkerVisible="true">
        <dc:Bounds x="1031" y="383" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="990" y="420" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0vidx1u_di" bpmnElement="SequenceFlow_0vidx1u">
        <di:waypoint x="927" y="480" />
        <di:waypoint x="927" y="408" />
        <di:waypoint x="1031" y="408" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0y6uvoi_di" bpmnElement="sequenceApproved_invoiceApproval">
        <di:waypoint x="1081" y="408" />
        <di:waypoint x="1292" y="408" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1085" y="392" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_08y2sd8_di" bpmnElement="sequenceRejected_invoiceApproval">
        <di:waypoint x="1056" y="383" />
        <di:waypoint x="1056" y="286" />
        <di:waypoint x="1292" y="286" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1039" y="355" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_053q9wi_di" bpmnElement="newInvoiceCreated_invoiceApproval">
        <dc:Bounds x="148" y="462" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="133" y="505" width="69" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1n24y9w_di">
        <di:waypoint x="423" y="495" />
        <di:waypoint x="530" y="495" />
        <di:waypoint x="530" y="545" />
        <di:waypoint x="572" y="545" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SendTask_1wd5pee_di" bpmnElement="sendApprovalNotification_invoiceApproval">
        <dc:Bounds x="572" y="505" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1t8fodz_di" bpmnElement="SequenceFlow_1t8fodz">
        <di:waypoint x="1280" y="520" />
        <di:waypoint x="1056" y="520" />
        <di:waypoint x="1056" y="433" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0uwfipu_di" bpmnElement="end5_invoiceApproval">
        <dc:Bounds x="1415" y="268" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1462" y="279" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0o7eg9h_di" bpmnElement="SequenceFlow_0o7eg9h">
        <di:waypoint x="1392" y="286" />
        <di:waypoint x="1415" y="286" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1nqrz3j_di" bpmnElement="end6_invoiceApproval">
        <dc:Bounds x="1415" y="404" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1459" y="415" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0v85vpc_di" bpmnElement="SequenceFlow_0v85vpc">
        <di:waypoint x="1392" y="422" />
        <di:waypoint x="1415" y="422" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1c9aodz_di" bpmnElement="autoRejectInvoice_invoiceApproval">
        <dc:Bounds x="1284" y="697" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0tfr5e9_di" bpmnElement="sendRejectNotification_invoiceApproval">
        <dc:Bounds x="1292" y="246" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1od76nh_di" bpmnElement="sendApproveNotification_invoiceApproval">
        <dc:Bounds x="1292" y="368" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0qvu7rx_di" bpmnElement="SequenceFlow_0qvu7rx">
        <di:waypoint x="803" y="545" />
        <di:waypoint x="840" y="545" />
        <di:waypoint x="840" y="520" />
        <di:waypoint x="877" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ke2v55_di" bpmnElement="SequenceFlow_0ke2v55">
        <di:waypoint x="672" y="545" />
        <di:waypoint x="753" y="545" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0e5o342_di" bpmnElement="SequenceFlow_0e5o342">
        <di:waypoint x="672" y="448" />
        <di:waypoint x="778" y="448" />
        <di:waypoint x="778" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0jtfilw_di">
        <di:waypoint x="184" y="480" />
        <di:waypoint x="323" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1u3p8wh_di">
        <di:waypoint x="419" y="518" />
        <di:waypoint x="508" y="518" />
        <di:waypoint x="508" y="617" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_18srail_di" bpmnElement="SequenceFlow_18srail">
        <di:waypoint x="400" y="664" />
        <di:waypoint x="311" y="664" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_0wgvagq_di" bpmnElement="end2_invoiceApproval">
        <dc:Bounds x="275" y="646" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="251" y="689" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ReceiveTask_04962ad_di" bpmnElement="waitForDeleteOrVoid_invoiceApproval">
        <dc:Bounds x="400" y="624" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0vn64gj_di" bpmnElement="end1_invoiceApproval">
        <dc:Bounds x="482" y="304" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="457" y="347" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0kjb13w_di">
        <di:waypoint x="419" y="442" />
        <di:waypoint x="419" y="322" />
        <di:waypoint x="482" y="322" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ParallelGateway_1gumdte_di" bpmnElement="parallelGateway_invoiceApproval">
        <dc:Bounds x="753" y="520" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_0sx7c5s_di" bpmnElement="SubProcess_0sx7c5s" isExpanded="true">
        <dc:Bounds x="678" y="81" width="400" height="130" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0u40fcr_di" bpmnElement="StartEvent_0u40fcr">
        <dc:Bounds x="700" y="143" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="706" y="186" width="24" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1rhv75h_di" bpmnElement="EndEvent_1rhv75h">
        <dc:Bounds x="1000" y="143" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1009" y="186" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0qc5h4h_di" bpmnElement="ServiceTask_0qc5h4h">
        <dc:Bounds x="818" y="121" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1ghw453_di" bpmnElement="SequenceFlow_1ghw453">
        <di:waypoint x="918" y="161" />
        <di:waypoint x="1000" y="161" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0aeokfr_di" bpmnElement="SequenceFlow_0aeokfr">
        <di:waypoint x="736" y="161" />
        <di:waypoint x="818" y="161" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BusinessRuleTask_06zco86_di" bpmnElement="invoiceApprovalDecision_invoiceApproval_companyId_uuid">
        <dc:Bounds x="230" y="450" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_09y5vl1_di" bpmnElement="SequenceFlow_09y5vl1">
        <di:waypoint x="184" y="480" />
        <di:waypoint x="230" y="480" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0utyhtt_di" bpmnElement="sequenceToApproval_invoiceApproval">
        <di:waypoint x="330" y="500" />
        <di:waypoint x="481" y="500" />
        <di:waypoint x="481" y="430" />
        <di:waypoint x="572" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0uzcz0e_di" bpmnElement="sequenceToNotify_invoiceApproval">
        <di:waypoint x="330" y="520" />
        <di:waypoint x="572" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0250404_di" bpmnElement="sequenceToWait1_invoiceApproval">
        <di:waypoint x="280" y="530" />
        <di:waypoint x="280" y="577" />
        <di:waypoint x="420" y="577" />
        <di:waypoint x="420" y="624" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0skvj0g_di" bpmnElement="SequenceFlow_0skvj0g">
        <di:waypoint x="330" y="480" />
        <di:waypoint x="406" y="480" />
        <di:waypoint x="406" y="322" />
        <di:waypoint x="482" y="322" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Task_0mc1xf4_di" bpmnElement="Task_0mc1xf4">
        <dc:Bounds x="230" y="560" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0tc7nq5_di" bpmnElement="SequenceFlow_0tc7nq5">
        <di:waypoint x="184" y="480" />
        <di:waypoint x="210" y="480" />
        <di:waypoint x="210" y="600" />
        <di:waypoint x="230" y="600" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
