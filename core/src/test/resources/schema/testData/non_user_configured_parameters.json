{"intuit_userid": {"handlerFieldName": "To", "configurable": false, "requiredByHandler": true, "requiredByUI": false, "multiSelect": false, "fieldType": "string", "valueType": "PROCESS_VARIABLE"}, "Subject": {"fieldValue": ["~custom.approval.sendRejectNotification.txnApproval.Subject"], "configurable": true, "actionByUI": null, "requiredByHandler": true, "requiredByUI": false, "multiSelect": false, "fieldType": "string"}, "DocNumber": {"configurable": false, "requiredByHandler": true, "requiredByUI": false, "multiSelect": false, "fieldType": "string", "valueType": "PROCESS_VARIABLE", "handlerFieldName": "DocNumber"}, "entityType": {"handlerFieldName": "RecordType", "configurable": false, "requiredByHandler": true, "requiredByUI": false, "multiSelect": false, "fieldType": "string", "valueType": "PROCESS_VARIABLE"}, "Message": {"fieldValue": ["~custom.approval.sendRejectNotification.txnApproval.Message"], "configurable": true, "requiredByHandler": true, "requiredByUI": true, "multiSelect": false, "fieldType": "string"}, "Id": {"handlerFieldName": "TxnId", "configurable": false, "requiredByHandler": true, "requiredByUI": false, "multiSelect": false, "fieldType": "string", "valueType": "PROCESS_VARIABLE"}, "intuit_realmid": {"handlerFieldName": "realmId", "configurable": false, "requiredByHandler": true, "requiredByUI": false, "multiSelect": false, "fieldType": "string", "valueType": "PROCESS_VARIABLE"}, "isEmail": {"fieldValue": ["true"], "configurable": true, "requiredByHandler": true, "requiredByUI": true, "multiSelect": false, "fieldType": "boolean"}, "isMobile": {"fieldValue": ["true"], "configurable": false, "requiredByHandler": true, "requiredByUI": true, "multiSelect": false, "fieldType": "boolean"}, "CC": {"configurable": true, "requiredByHandler": true, "requiredByUI": true, "multiSelect": false, "fieldType": "string"}, "BCC": {"configurable": true, "requiredByHandler": true, "requiredByUI": true, "helpVariables": ["Customer <PERSON><PERSON>"], "multiSelect": false, "fieldType": "string"}, "NotificationAction": {"fieldValue": ["qb001://open/{{RecordType}}/?id=[[{{RecordType}}LocalId]]&companyid=[[realmId]]"], "configurable": false, "requiredByHandler": true, "requiredByUI": false, "multiSelect": false, "fieldType": "string", "handlerFieldName": "Mobile Notification Action"}, "CompanyName": {"configurable": false, "requiredByHandler": true, "requiredByUI": false, "multiSelect": false, "fieldType": "string", "valueType": "PROCESS_VARIABLE", "handlerFieldName": "CompanyName"}}