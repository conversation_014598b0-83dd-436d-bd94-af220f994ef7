{"eventHeaders": {"workflow": "approval", "entityType": "invoice", "entityChangeType": "created", "entityId": "19670010"}, "variables": {"local": {"DepartmentRef": {"name": "dep1", "value": "1"}, "TotalAmt": 80000, "test": "test123", "CustomerRef": {"name": "Customer1", "value": "1"}}, "global": {"DepartmentRef": {"name": "dep1", "value": "1-global"}, "TotalAmt": 80000, "test": "test123", "CustomerRef": {"name": "Customer1", "value": "1-global"}}}}