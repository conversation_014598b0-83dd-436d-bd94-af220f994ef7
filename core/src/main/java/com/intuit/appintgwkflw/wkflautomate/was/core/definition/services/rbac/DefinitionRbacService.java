package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.rbac;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionRbacConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Service class responsible for handling Role-Based Access Control (RBAC) for Definition operations.
 * This service abstracts RBAC logic from both provider and service layers, providing a clean
 * separation of concerns for authorization checks.
 * 
 * <AUTHOR> Team
 */
@Service
@AllArgsConstructor
public class DefinitionRbacService {

    private final AccessVerifier accessVerifier;
    private final DefinitionRbacConfig definitionRbacConfig;
    private final DefinitionServiceHelper definitionServiceHelper;

    /**
     * Verifies RBAC access for Definition CRUD operations
     * 
     * @param definition the Definition entity (can be null for read operations)
     * @param operation the CRUD operation being performed
     * @param ownerId the owner ID for context (used for read operations when definition is null)
     * @throws WorkflowGeneralException if access is denied
     */
    public void verifyDefinitionAccess(Definition definition, CrudOperation operation, String ownerId) {
        if (!shouldApplyRbac()) {
            return; // RBAC is disabled, allow access
        }

        String workflowType = determineWorkflowType(definition, ownerId);
        verifyRbacAccess(workflowType, operation);
    }

    /**
     * Verifies RBAC access for Definition CRUD operations (overloaded method for operations with Definition)
     * 
     * @param definition the Definition entity
     * @param operation the CRUD operation being performed
     * @throws WorkflowGeneralException if access is denied
     */
    public void verifyDefinitionAccess(Definition definition, CrudOperation operation) {
        verifyDefinitionAccess(definition, operation, null);
    }

    /**
     * Verifies RBAC access for read operations using definition ID
     * 
     * @param definitionId the definition ID
     * @param ownerId the owner ID
     * @param operation the CRUD operation (typically READ)
     * @throws WorkflowGeneralException if access is denied
     */
    public void verifyDefinitionAccessById(String definitionId, String ownerId, CrudOperation operation) {
        if (!shouldApplyRbac()) {
            return; // RBAC is disabled, allow access
        }

        String workflowType = getWorkflowTypeById(definitionId, ownerId);
        verifyRbacAccess(workflowType, operation);
    }

    /**
     * Determines the CRUD operation type for write operations based on Definition state
     * 
     * @param definition the Definition entity
     * @return the CRUD operation type
     */
    public CrudOperation determineWriteOperation(Definition definition) {
        if (definition == null) {
            return CrudOperation.CREATE;
        }

        CrudOperation operation = CrudOperation.CREATE;

        if (definition.isIdSet()) {
            operation = CrudOperation.UPDATE;
        }

        if (definition.isDeletedSet() && definition.isDeleted().booleanValue()) {
            operation = CrudOperation.DELETE;
        } else if (definition.isStatusSet()
                && WorkflowStatusEnum.ENABLED == definition.getStatus()
                && !definition.isWorkflowStepsSet()) {
            operation = CrudOperation.ENABLED;
        } else if (definition.isStatusSet()
                && WorkflowStatusEnum.DISABLED == definition.getStatus()
                && !definition.isWorkflowStepsSet()) {
            operation = CrudOperation.DISABLED;
        }

        return operation;
    }

    /**
     * Determines if RBAC should be applied
     * 
     * @return true if RBAC should be applied
     */
    private boolean shouldApplyRbac() {
        return definitionRbacConfig.isEnabled();
    }

    /**
     * Performs the actual RBAC access verification
     * 
     * @param workflowType the workflow type
     * @param operation the CRUD operation
     * @throws WorkflowGeneralException if access is denied
     */
    private void verifyRbacAccess(String workflowType, CrudOperation operation) {
        boolean hasAccess = accessVerifier.verifyUserAccess(workflowType, operation.name());
        if (!hasAccess) {
            throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
        }
    }

    /**
     * Determines the workflow type from a Definition entity
     * 
     * @param definition the Definition entity (can be null)
     * @param ownerId the owner ID for fallback context
     * @return the workflow type for RBAC checks
     */
    private String determineWorkflowType(Definition definition, String ownerId) {
        if (definition != null && definition.isIdSet()) {
            // Extract definition ID and use helper to get workflow type
            String definitionId = definition.getId().getLocalId();
            return getWorkflowTypeById(definitionId, ownerId);
        } else if (definition != null && definition.isTemplateSet() && definition.getTemplate().isNameSet()) {
            // For new definitions, determine workflow type from template
            String templateName = definition.getTemplate().getName();
            return CustomWorkflowType.getActionKey(templateName);
        }
        
        // Default fallback for read operations or when context is unclear
        return "default";
    }

    /**
     * Gets workflow type by definition ID using DefinitionServiceHelper
     * 
     * @param definitionId the definition ID
     * @param ownerId the owner ID
     * @return the workflow type
     */
    private String getWorkflowTypeById(String definitionId, String ownerId) {
        try {
            return definitionServiceHelper.getWorkflowType(definitionId, ownerId);
        } catch (Exception e) {
            // If we can't determine the workflow type, default to 'default'
            // This ensures RBAC doesn't fail due to missing context
            return "default";
        }
    }
}
