package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowCoreConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.DownStreamConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Returns Camunda url for the offering
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class CamundaRestUtil {

  private OfferingConfig offeringConfig;
  private WorkflowCoreConfig workflowCoreConfig;

  /**
   * To get camunda urls for offerings at start up
   *
   * @param offeringId
   * @return
   */
  public String getCamundaBaseURL(Optional<String> offeringId) {
    return getBaseUrl(getSource(offeringId));
  }

  /**
   * Get camunda url for an offering
   *
   * @return
   */
  public String getCamundaBaseURL() {
    return getCamundaBaseURL(WASContext.getOfferingId());
  }

  /**
   * Get camunda host end point for an offering
   *
   * @return
   */
  public String getCamundaHostUrl() {
    return getHostUrl(getSource(WASContext.getOfferingId()));
  }

  private Optional<DownStreamConfig> getSource(Optional<String> offeringId) {
    if (offeringId.isEmpty()) {
      return Optional.empty();
    }

    // TODO possible optimization - create a map for the array to avoid iteration
    return offeringConfig
        .getDownstreamServices()
        .stream()
        .filter(offering -> offering.getOfferingId().equals(offeringId.get()))
        .findFirst();
  }

  private String getBaseUrl(Optional<DownStreamConfig> source) {
    return source
        .map(x -> x.getWorkflowCore())
        .map(x -> x.getBaseUrl())
        .orElseGet(
            () -> {
              WorkflowLogger.logWarn("Base url not set, using default camunda base url");
              return workflowCoreConfig.getRestEndpointPrefix();
            });
  }

  private String getHostUrl(Optional<DownStreamConfig> source) {
    return source
        .map(x -> x.getWorkflowCore())
        .map(x -> x.getHostUrl())
        .orElseGet(
            () -> {
              WorkflowLogger.logWarn("Host url not set, using default camunda base url");
              return workflowCoreConfig.getHostEndpointPrefix();
            });
  }
}
