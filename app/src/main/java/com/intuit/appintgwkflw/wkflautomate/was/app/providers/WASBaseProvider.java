package com.intuit.appintgwkflw.wkflautomate.was.app.providers;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.CommonExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.Authorization;
import com.intuit.v4.Error;
import com.intuit.v4.GlobalId;
import com.intuit.v4.Model;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.providers.SingleResult;
import com.intuit.v4.providers.StandardProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Author: Nitin Gupta <br>
 * Date: 17/01/20 <br>
 * Description: This is base provider for WAS services, every provider needs to extend this
 * provider. This provider provides following capabilities 1. Inject auth header to MDC 2. Handler
 * some error msgs wrapping
 */
@Component
public abstract class WASBaseProvider<T extends Model<?>> extends StandardProvider<T> {

  @Autowired private WASContextHandler contextHandler;

  @Override
  protected final SingleResult<T> readOne(
          RequestContext context, @SuppressWarnings("rawtypes") GlobalId id, QueryHelper query) {
    populateMDCHeaders(context);
    return CommonExecutor.execute(
            () -> wasReadOne(context, id, query),
            "Exception occurred while performing read one operation ",
            e -> handleServiceException((WorkflowGeneralException) e),
            e1 -> handleSystemError(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage()));
  }

  @Override
  protected final ListResult<T> readList(RequestContext context, QueryHelper query) {
    populateMDCHeaders(context);
    return CommonExecutor.execute(
            () -> wasReadList(context, query),
            "Exception occurred while performing read list operation ",
            e -> handleServiceExceptionList((WorkflowGeneralException) e),
            e1 -> handleSystemErrorList(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage()));
  }

  @Override
  protected final SingleResult<T> writeOne(RequestContext context, T entity) {
    populateMDCHeaders(context);
    return CommonExecutor.execute(
            () -> wasWriteOne(context, entity),
            "Exception occurred while performing write one operation ",
            e -> handleServiceException((WorkflowGeneralException) e),
            e1 -> handleSystemError(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage()));
  }

  /**
   * populate MDC required params
   *
   * @param context input request context
   */
  protected void populateMDCHeaders(RequestContext context) {
    Authorization authorization =
            new Authorization(
                    context.getCustomHeaders().get(WorkflowConstants.AUTHORIZATION_HEADER.toLowerCase()));
    context.setAuthorization(authorization);
    contextHandler.addKey(
            WASContextEnums.AUTHORIZATION_HEADER, context.getAuthorization().toString());
    contextHandler.addKey(WASContextEnums.OWNER_ID, context.getAuthorization().getRealm());
  }

  protected abstract SingleResult<T> wasReadOne(
          RequestContext context, @SuppressWarnings("rawtypes") GlobalId id, QueryHelper query);

  protected abstract ListResult<T> wasReadList(RequestContext context, QueryHelper query);

  protected abstract SingleResult<T> wasWriteOne(RequestContext context, T entity)
          throws WorkflowGeneralException;

  // ========== Error Building ==========
  private Error getError(Error.ErrorTypeEnum errorTypeEnum, String errorCode, String msg) {

    Error error = new Error().type(errorTypeEnum).code(errorCode).message(msg);
    WorkflowLogger.logError(
            "event=txnSaveError, txnErrorCode=%s , txnErrorDetails=%s",
            error.getCode(), error.getMessage());
    return error;
  }

  protected SingleResult<T> handleSystemError(String msg) {
    Error error =
            getError(
                    Error.ErrorTypeEnum.SYSTEM_ERROR, WorkflowError.INTERNAL_EXCEPTION.getErrorCode(), msg);

    return SingleResult.of(error);
  }

  protected ListResult<T> handleSystemErrorList(String msg) {
    Error error =
            getError(
                    Error.ErrorTypeEnum.SYSTEM_ERROR, WorkflowError.INTERNAL_EXCEPTION.getErrorCode(), msg);

    return ListResult.of(error);
  }

  protected SingleResult<T> handleUnSupportedOperation(String msg) {
    Error error =
            getError(
                    Error.ErrorTypeEnum.INVALID_REQUEST,
                    WorkflowError.UNSUPPORTED_OPERATION.getErrorCode(),
                    msg);
    return SingleResult.of(error);
  }

  protected SingleResult<T> handleServiceException(WorkflowGeneralException ex) {
    WorkflowLogger.logError(
            "event=txnSaveError, txnErrorCode=%s, txnErrorDetails=%s",
            ex.getError().getCode(), ex.getError().getMessage());
    return SingleResult.of(ex.getError());
  }

  protected ListResult<T> handleServiceExceptionList(WorkflowGeneralException ex) {
    WorkflowLogger.logError(
            "event=txnSaveError, txnErrorCode=%s, txnErrorDetails=%s",
            ex.getError().getCode(), ex.getError().getMessage());
    return ListResult.of(ex.getError());
  }

  protected void verifyRealmId(Authorization authorization){
    boolean expression = WASContext.isNonRealmSystemUser()
            || StringUtils.isEmpty(authorization.getRealm());
    WorkflowVerfiy.verify(expression, WorkflowError.INVALID_REALM_ID);
  }

  /**
   * This verifies realm is test drive or not.
   *
   * @param authorization
   */
  protected void verifyTestDriveRealmId(Authorization authorization) {
    WorkflowVerfiy.verify(WasUtils.isTestDriveRealm(authorization.getRealm()),
            WorkflowError.CREATION_NOT_SUPPORTED);
  }
}